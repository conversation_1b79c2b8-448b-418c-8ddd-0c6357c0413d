{"__meta": {"id": "X3cd07212ae130226b6e6a83be22ac470", "datetime": "2025-07-28 16:48:30", "utime": **********.14755, "method": "GET", "uri": "/user", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 17, "messages": [{"message": "[16:48:27] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.745454, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:27] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.790622, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:27] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.790802, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:27] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.790871, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:27] LOG.warning: Optional parameter $serviceProviderId declared before required parameter $search is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\UserTrait.php on line 328", "message_html": null, "is_string": false, "label": "warning", "time": **********.793022, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:27] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2418", "message_html": null, "is_string": false, "label": "warning", "time": **********.852124, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:27] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2478", "message_html": null, "is_string": false, "label": "warning", "time": **********.8522, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:27] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3619", "message_html": null, "is_string": false, "label": "warning", "time": **********.853302, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:27] LOG.debug: Checking existence of file: uploads/profile_images/**********.png", "message_html": null, "is_string": false, "label": "debug", "time": **********.895145, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:29] LOG.debug: File exists: false", "message_html": null, "is_string": false, "label": "debug", "time": **********.496477, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:29] LOG.debug: Checking existence of file: uploads/profile_images/**********.png", "message_html": null, "is_string": false, "label": "debug", "time": **********.519249, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:29] LOG.debug: File exists: false", "message_html": null, "is_string": false, "label": "debug", "time": **********.750888, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:29] LOG.debug: Checking existence of file: uploads/profile_images/**********.png", "message_html": null, "is_string": false, "label": "debug", "time": **********.751305, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:29] LOG.debug: File exists: false", "message_html": null, "is_string": false, "label": "debug", "time": **********.984827, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:30] LOG.warning: Optional parameter $filters declared before required parameter $userServiceProvider is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\WorkOrdersTrait.php on line 3418", "message_html": null, "is_string": false, "label": "warning", "time": **********.021793, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:30] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 637", "message_html": null, "is_string": false, "label": "warning", "time": **********.042431, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:30] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\f8d18d6f63e9bf36e98d3bb1b82c9e7d86b4c71c.php on line 3", "message_html": null, "is_string": false, "label": "warning", "time": **********.094349, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.193259, "end": **********.14759, "duration": 2.9543309211730957, "duration_str": "2.95s", "measures": [{"label": "Booting", "start": **********.193259, "relative_start": 0, "end": **********.721728, "relative_end": **********.721728, "duration": 0.5284690856933594, "duration_str": "528ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.721754, "relative_start": 0.5284950733184814, "end": **********.147591, "relative_end": 1.1920928955078125e-06, "duration": 2.4258370399475098, "duration_str": "2.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 60525488, "peak_usage_str": "58MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 8, "templates": [{"name": "applications.admin.user.user-profile (\\resources\\views\\applications\\admin\\user\\user-profile.blade.php)", "param_count": 14, "params": ["data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.app (\\resources\\views\\layouts\\app.blade.php)", "param_count": 28, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "hideclass", "__currentLoopData", "country", "loop", "city", "attempts", "lastAttempt", "limit", "timeLimit", "remainingTime", "remainingText"], "type": "blade"}, {"name": "layouts.partials._styles (\\resources\\views\\layouts\\partials\\_styles.blade.php)", "param_count": 28, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "hideclass", "__currentLoopData", "country", "loop", "city", "attempts", "lastAttempt", "limit", "timeLimit", "remainingTime", "remainingText"], "type": "blade"}, {"name": "layouts.partials._header (\\resources\\views\\layouts\\partials\\_header.blade.php)", "param_count": 28, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "hideclass", "__currentLoopData", "country", "loop", "city", "attempts", "lastAttempt", "limit", "timeLimit", "remainingTime", "remainingText"], "type": "blade"}, {"name": "layouts.partials._top_menu (\\resources\\views\\layouts\\partials\\_top_menu.blade.php)", "param_count": 28, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "hideclass", "__currentLoopData", "country", "loop", "city", "attempts", "lastAttempt", "limit", "timeLimit", "remainingTime", "remainingText"], "type": "blade"}, {"name": "livewire.menu.aside-nav-list (\\resources\\views\\livewire\\menu\\aside-nav-list.blade.php)", "param_count": 27, "params": ["userPrivilegesAside", "user", "hasViewPrivilege", "errors", "_instance", "has<PERSON>dmin", "projectId", "project", "workOrderMenuItemColor", "flagWorkorderSidebarMenu", "userPrivileges", "closedWorkOrderCount", "maintenanceRequestCount", "vendorRegistrationApplicationRequests", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.partials._footer (\\resources\\views\\layouts\\partials\\_footer.blade.php)", "param_count": 31, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "hideclass", "__currentLoopData", "country", "loop", "city", "attempts", "lastAttempt", "limit", "timeLimit", "remainingTime", "remainingText", "html", "url", "segments"], "type": "blade"}, {"name": "layouts.partials._scripts (\\resources\\views\\layouts\\partials\\_scripts.blade.php)", "param_count": 31, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "hideclass", "__currentLoopData", "country", "loop", "city", "attempts", "lastAttempt", "limit", "timeLimit", "remainingTime", "remainingText", "html", "url", "segments"], "type": "blade"}]}, "route": {"uri": "GET user", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Admin\\User\\UserControllerNew@profile", "as": "users.profile", "namespace": "App\\Http\\Controllers\\Admin\\User", "prefix": "/user", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php&line=3230\">\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php:3230-3241</a>"}, "queries": {"nb_statements": 12, "nb_failed_statements": 0, "accumulated_duration": 0.025119999999999996, "accumulated_duration_str": "25.12ms", "statements": [{"sql": "select * from `users` where `id` = 21 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00457, "duration_str": "4.57ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 18.193}, {"sql": "select * from `countries` where `status` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php", "line": 3234}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00535, "duration_str": "5.35ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php:3234", "connection": "osool_test_db", "start_percent": 18.193, "width_percent": 21.298}, {"sql": "select * from `cities` where `status` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php", "line": 3236}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00281, "duration_str": "2.81ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php:3236", "connection": "osool_test_db", "start_percent": 39.49, "width_percent": 11.186}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 0 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Providers\\AsideViewComposerServiceProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.00074, "duration_str": "740μs", "stmt_id": "\\app\\Providers\\AsideViewComposerServiceProvider.php:59", "connection": "osool_test_db", "start_percent": 50.677, "width_percent": 2.946}, {"sql": "select `name`, `name_ar` from `user_type` where `slug` = 'super_admin' limit 1", "type": "query", "params": [], "bindings": ["super_admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1929}, {"index": 14, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 161}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.0025, "duration_str": "2.5ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1929", "connection": "osool_test_db", "start_percent": 53.623, "width_percent": 9.952}, {"sql": "select `id`, `project_image`, `use_beneficiary_module`, `use_tenant_module`, `benificiary_status`, `tenant_status`, `project_name`, `project_name_ar`, `use_crm_module` from `projects_details` where `id` = 0 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\ProjectDetailTrait.php", "line": 11}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 128}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 71}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.0014399999999999999, "duration_str": "1.44ms", "stmt_id": "\\app\\Http\\Traits\\ProjectDetailTrait.php:11", "connection": "osool_test_db", "start_percent": 63.575, "width_percent": 5.732}, {"sql": "select `id`, `created_at` from `users` where `project_id` = 0 and `user_type` = 'admin' and `status` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["0", "admin", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\UserTrait.php", "line": 256}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Traits\\UserTrait.php", "line": 267}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 158}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 74}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\app\\Http\\Traits\\UserTrait.php:256", "connection": "osool_test_db", "start_percent": 69.307, "width_percent": 2.747}, {"sql": "select count(*) as aggregate from `vendor_profiles` where `submit_status` = 'submit' and `application_status` = 'no_action' and `vendor_profiles`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["submit", "no_action"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 220}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 60}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0020800000000000003, "duration_str": "2.08ms", "stmt_id": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php:220", "connection": "osool_test_db", "start_percent": 72.054, "width_percent": 8.28}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (21) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\PermissionServiceProvider.php", "line": 126}, {"index": 23, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\PermissionServiceProvider.php", "line": 137}, {"index": 27, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 55}, {"index": 29, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}], "duration": 0.00227, "duration_str": "2.27ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:241", "connection": "osool_test_db", "start_percent": 80.334, "width_percent": 9.037}, {"sql": "select exists(select * from `projects_details` where `id` = 0 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["0", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 897}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_test_db", "start_percent": 89.371, "width_percent": 3.702}, {"sql": "select `id` from `users` where `project_id` = 0 and `user_type` = 'admin' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["0", "admin", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 928}, {"index": 14, "namespace": "view", "name": "f8d18d6f63e9bf36e98d3bb1b82c9e7d86b4c71c", "line": 13}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00103, "duration_str": "1.03ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:928", "connection": "osool_test_db", "start_percent": 93.073, "width_percent": 4.1}, {"sql": "select exists(select * from `projects_details` where `id` = 0 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["0", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 183}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_test_db", "start_percent": 97.174, "width_percent": 2.826}]}, "models": {"data": {"App\\Models\\Auth\\Role": 1, "App\\Models\\City": 106, "App\\Models\\Country": 252, "App\\Models\\User": 2}, "count": 361}, "livewire": {"data": {"menu.aside-nav-list #": "array:7 [\n  \"data\" => array:10 [\n    \"user\" => App\\Models\\User {#3575\n      #connection: \"mysql\"\n      #table: \"users\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:78 [\n        \"id\" => 21\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$fDWCcrhfM04qytFJLBRif.FxX3wOI.YE0hyJ3HLOYureXOvSLc4ZG\"\n        \"name\" => \"Super Admin\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => \"123456789\"\n        \"profile_img\" => \"**********.png\"\n        \"emp_id\" => \"1234112\"\n        \"profession_id\" => null\n        \"emp_dept\" => \"Management\"\n        \"building_ids\" => null\n        \"contract_ids\" => \"\"\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => \"\"\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => \"1,2,3,4\"\n        \"role_cities\" => \"1,2,3,4\"\n        \"asset_categories\" => \"1,2,3,4\"\n        \"keeper_warehouses\" => null\n        \"properties\" => \"1\"\n        \"contracts\" => \"1\"\n        \"beneficiary\" => \"1\"\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"super_admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 0\n        \"project_id\" => 0\n        \"project_user_id\" => 0\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 0\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2022-03-03 13:52:49\"\n        \"modified_at\" => \"2025-07-28 16:46:58\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => null\n        \"crm_api_token\" => null\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #original: array:78 [\n        \"id\" => 21\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$fDWCcrhfM04qytFJLBRif.FxX3wOI.YE0hyJ3HLOYureXOvSLc4ZG\"\n        \"name\" => \"Super Admin\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => \"123456789\"\n        \"profile_img\" => \"**********.png\"\n        \"emp_id\" => \"1234112\"\n        \"profession_id\" => null\n        \"emp_dept\" => \"Management\"\n        \"building_ids\" => null\n        \"contract_ids\" => \"\"\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => \"\"\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => \"1,2,3,4\"\n        \"role_cities\" => \"1,2,3,4\"\n        \"asset_categories\" => \"1,2,3,4\"\n        \"keeper_warehouses\" => null\n        \"properties\" => \"1\"\n        \"contracts\" => \"1\"\n        \"beneficiary\" => \"1\"\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"super_admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 0\n        \"project_id\" => 0\n        \"project_user_id\" => 0\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 0\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2022-03-03 13:52:49\"\n        \"modified_at\" => \"2025-07-28 16:46:58\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => null\n        \"crm_api_token\" => null\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #changes: []\n      #casts: array:2 [\n        \"email_verified_at\" => \"datetime\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:2 [\n        \"projectDetails\" => null\n        \"roles\" => Illuminate\\Database\\Eloquent\\Collection {#4708\n          #items: array:1 [\n            0 => App\\Models\\Auth\\Role {#4753\n              #connection: \"mysql\"\n              #table: \"roles\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 1\n                \"name\" => \"super_admin\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2024-09-26 17:32:34\"\n                \"updated_at\" => \"2024-09-26 17:32:34\"\n              ]\n              #original: array:8 [\n                \"id\" => 1\n                \"name\" => \"super_admin\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2024-09-26 17:32:34\"\n                \"updated_at\" => \"2024-09-26 17:32:34\"\n                \"pivot_model_id\" => 21\n                \"pivot_role_id\" => 1\n                \"pivot_model_type\" => \"App\\Models\\User\"\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\MorphPivot {#4754\n                  #connection: \"mysql\"\n                  #table: \"model_has_roles\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:3 [\n                    \"model_id\" => 21\n                    \"role_id\" => 1\n                    \"model_type\" => \"App\\Models\\User\"\n                  ]\n                  #original: array:3 [\n                    \"model_id\" => 21\n                    \"role_id\" => 1\n                    \"model_type\" => \"App\\Models\\User\"\n                  ]\n                  #changes: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dates: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  +timestamps: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: App\\Models\\User {#4620\n                    #connection: \"mysql\"\n                    #table: \"users\"\n                    #primaryKey: \"id\"\n                    #keyType: \"int\"\n                    +incrementing: true\n                    #with: []\n                    #withCount: []\n                    +preventsLazyLoading: false\n                    #perPage: 15\n                    +exists: false\n                    +wasRecentlyCreated: false\n                    #escapeWhenCastingToString: false\n                    #attributes: []\n                    #original: []\n                    #changes: []\n                    #casts: array:2 [\n                      \"email_verified_at\" => \"datetime\"\n                      \"deleted_at\" => \"datetime\"\n                    ]\n                    #classCastCache: []\n                    #attributeCastCache: []\n                    #dates: []\n                    #dateFormat: null\n                    #appends: []\n                    #dispatchesEvents: []\n                    #observables: []\n                    #relations: []\n                    #touches: []\n                    +timestamps: true\n                    #hidden: array:2 [\n                      0 => \"password\"\n                      1 => \"remember_token\"\n                    ]\n                    #visible: []\n                    #fillable: array:62 [\n                      0 => \"allow_akaunting\"\n                      1 => \"email\"\n                      2 => \"password\"\n                      3 => \"name\"\n                      4 => \"first_name\"\n                      5 => \"last_name\"\n                      6 => \"apartment\"\n                      7 => \"unit_receival_date\"\n                      8 => \"later_booking_alert\"\n                      9 => \"phone\"\n                      10 => \"profile_img\"\n                      11 => \"address\"\n                      12 => \"country_id\"\n                      13 => \"city_id\"\n                      14 => \"role_regions\"\n                      15 => \"role_cities\"\n                      16 => \"asset_categories\"\n                      17 => \"properties\"\n                      18 => \"contracts\"\n                      19 => \"beneficiary\"\n                      20 => \"service_provider\"\n                      21 => \"user_type\"\n                      22 => \"project_id\"\n                      23 => \"project_user_id\"\n                      24 => \"created_by\"\n                      25 => \"status\"\n                      26 => \"user_privileges\"\n                      27 => \"approved_max_amount\"\n                      28 => \"emp_id\"\n                      29 => \"profession_id\"\n                      30 => \"emp_dept\"\n                      31 => \"building_ids\"\n                      32 => \"contract_ids\"\n                      33 => \"supervisor_id\"\n                      34 => \"sp_admin_id\"\n                      35 => \"langForSms\"\n                      36 => \"deleted_at\"\n                      37 => \"otp\"\n                      38 => \"temp_password\"\n                      39 => \"otp_for_password\"\n                      40 => \"otp_for_password_verified\"\n                      41 => \"temp_phone_number\"\n                      42 => \"favorite_language\"\n                      43 => \"is_subcontractors_worker\"\n                      44 => \"keeper_warehouses\"\n                      45 => \"save_later_date\"\n                      46 => \"first_login\"\n                      47 => \"is_unit_link\"\n                      48 => \"akaunting_vendor_id\"\n                      49 => \"akaunting_customer_id\"\n                      50 => \"crm_api_token\"\n                      51 => \"workspace_slug\"\n                      52 => \"is_bma_area_manager\"\n                      53 => \"assigned_workers\"\n                      54 => \"sleep_mode\"\n                      55 => \"offline_mode\"\n                      56 => \"attendance_mandatory\"\n                      57 => \"admin_level\"\n                      58 => \"role\"\n                      59 => \"attendance_target\"\n                      60 => \"salary\"\n                      61 => \"show_extra_info\"\n                    ]\n                    #guarded: array:1 [\n                      0 => \"*\"\n                    ]\n                    #rememberTokenName: \"remember_token\"\n                    #accessToken: null\n                    #forceDeleting: false\n                    #excludedAttributes: []\n                    +auditEvent: null\n                    +auditCustomOld: null\n                    +auditCustomNew: null\n                    +isCustomEvent: false\n                    +preloadedResolverData: []\n                    -roleClass: null\n                    -permissionClass: null\n                    -wildcardClass: null\n                  }\n                  #foreignKey: \"model_id\"\n                  #relatedKey: \"role_id\"\n                  #morphType: \"model_type\"\n                  #morphClass: \"App\\Models\\User\"\n                }\n              ]\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -permissionClass: null\n              -wildcardClass: null\n            }\n          ]\n          #escapeWhenCastingToString: false\n        }\n      ]\n      #touches: []\n      +timestamps: true\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: array:62 [\n        0 => \"allow_akaunting\"\n        1 => \"email\"\n        2 => \"password\"\n        3 => \"name\"\n        4 => \"first_name\"\n        5 => \"last_name\"\n        6 => \"apartment\"\n        7 => \"unit_receival_date\"\n        8 => \"later_booking_alert\"\n        9 => \"phone\"\n        10 => \"profile_img\"\n        11 => \"address\"\n        12 => \"country_id\"\n        13 => \"city_id\"\n        14 => \"role_regions\"\n        15 => \"role_cities\"\n        16 => \"asset_categories\"\n        17 => \"properties\"\n        18 => \"contracts\"\n        19 => \"beneficiary\"\n        20 => \"service_provider\"\n        21 => \"user_type\"\n        22 => \"project_id\"\n        23 => \"project_user_id\"\n        24 => \"created_by\"\n        25 => \"status\"\n        26 => \"user_privileges\"\n        27 => \"approved_max_amount\"\n        28 => \"emp_id\"\n        29 => \"profession_id\"\n        30 => \"emp_dept\"\n        31 => \"building_ids\"\n        32 => \"contract_ids\"\n        33 => \"supervisor_id\"\n        34 => \"sp_admin_id\"\n        35 => \"langForSms\"\n        36 => \"deleted_at\"\n        37 => \"otp\"\n        38 => \"temp_password\"\n        39 => \"otp_for_password\"\n        40 => \"otp_for_password_verified\"\n        41 => \"temp_phone_number\"\n        42 => \"favorite_language\"\n        43 => \"is_subcontractors_worker\"\n        44 => \"keeper_warehouses\"\n        45 => \"save_later_date\"\n        46 => \"first_login\"\n        47 => \"is_unit_link\"\n        48 => \"akaunting_vendor_id\"\n        49 => \"akaunting_customer_id\"\n        50 => \"crm_api_token\"\n        51 => \"workspace_slug\"\n        52 => \"is_bma_area_manager\"\n        53 => \"assigned_workers\"\n        54 => \"sleep_mode\"\n        55 => \"offline_mode\"\n        56 => \"attendance_mandatory\"\n        57 => \"admin_level\"\n        58 => \"role\"\n        59 => \"attendance_target\"\n        60 => \"salary\"\n        61 => \"show_extra_info\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #rememberTokenName: \"remember_token\"\n      #accessToken: null\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n    }\n    \"hasAdmin\" => 215\n    \"projectId\" => null\n    \"project\" => null\n    \"workOrderMenuItemColor\" => null\n    \"flagWorkorderSidebarMenu\" => false\n    \"userPrivileges\" => null\n    \"closedWorkOrderCount\" => null\n    \"maintenanceRequestCount\" => 0\n    \"vendorRegistrationApplicationRequests\" => 2\n  ]\n  \"oldData\" => null\n  \"actionQueue\" => null\n  \"name\" => \"menu.aside-nav-list\"\n  \"view\" => \"livewire.menu.aside-nav-list\"\n  \"component\" => \"App\\Http\\Livewire\\Menu\\AsideNavList\"\n  \"id\" => null\n]"}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"locale": "en", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "izyAMXVfTmNLIYwsWk1sKlgE1ngcloby6jsfh6pj", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/user\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "21", "plain_user_password": "123456", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/user", "status_code": "<pre class=sf-dump id=sf-dump-80209586 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-80209586\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1365811267 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1365811267\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1632481449 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1632481449\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-210500984 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/workspace/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6Ijc3VEc2MytKWklRUUl2dWk2N0pVOFE9PSIsInZhbHVlIjoiVnYvdXM5M2RVa2xjS0dOWnZZL3VSZE1aTHlabXZFZ1N1NGljdVNXd1RFZ3NEZHVlTzZxcUExVjBSZkg2aU44Vyt4OTMyUC80RXRSS3FlTEJyMzRBNUpUblkyM0VVZ3MrWklYUncxNlJlS3VoMm5uZTZRNnFrTkJpd2g3eHNXNVYiLCJtYWMiOiJjMGI2NjI4YjAxNWE1ZjdlZWIyNjZiMjM5MDM0ODQxYmI0YTdkOTMxMGViMGE5NzkwYWQwOTk4MWVkNTNjMDI1IiwidGFnIjoiIn0%3D; osool_session=tXrmyU0dpMAodrI3bNXRPjEIJhkTAk92MpJxIO0O</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-210500984\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-800387900 data-indent-pad=\"  \"><span class=sf-dump-note>array:39</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/workspace/projects</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6Ijc3VEc2MytKWklRUUl2dWk2N0pVOFE9PSIsInZhbHVlIjoiVnYvdXM5M2RVa2xjS0dOWnZZL3VSZE1aTHlabXZFZ1N1NGljdVNXd1RFZ3NEZHVlTzZxcUExVjBSZkg2aU44Vyt4OTMyUC80RXRSS3FlTEJyMzRBNUpUblkyM0VVZ3MrWklYUncxNlJlS3VoMm5uZTZRNnFrTkJpd2g3eHNXNVYiLCJtYWMiOiJjMGI2NjI4YjAxNWE1ZjdlZWIyNjZiMjM5MDM0ODQxYmI0YTdkOTMxMGViMGE5NzkwYWQwOTk4MWVkNTNjMDI1IiwidGFnIjoiIn0%3D; osool_session=tXrmyU0dpMAodrI3bNXRPjEIJhkTAk92MpJxIO0O</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52235</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"5 characters\">/user</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"5 characters\">/user</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.1933</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-800387900\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-352076799 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">izyAMXVfTmNLIYwsWk1sKlgE1ngcloby6jsfh6pj</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-352076799\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-337379260 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 13:48:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImtJOCtMTFpoSDdNWHBwZUhCemczRlE9PSIsInZhbHVlIjoiREdxRE5tQUozOHNWUERTVzJuRFFMS01iTHRUdzltTkVTS0hmTWpIbStxZDZPb2l1WGM0V0lLb09OUWpVVFk2OEo2dnArRm1NcktRNzJCYXhKYjVMMkYvU3hyODJFenM1eWlOSStnTVkxRzhJcVFZdlMzbFR6YUVZRFkyaXlaWmsiLCJtYWMiOiJkYTE3MDEyM2ZlYzE4YjgzYTBiODQyMmQ1ZGNhODU0MjcyY2Q3ZGU1MmY5MGNmYWFmMmRlZWY5NzhhMWYzY2QxIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:48:30 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IjBiUEpKTlc2aThkN0pCUGY3R21JMnc9PSIsInZhbHVlIjoiZHloNis4amdya08ybWVCYWRCNUlTb1R4WjE1eG1SNjhSNGtQMnBXUWc1YkVVVDNUWVRHY0VTSG9mQURGWk1mT0JvSXI5c1l3aVFycXBVVHBhblBxblpqaVREdEV1V1Fzc0hIUG5NR091cHdxdVYwRUg3K3g2U3UzdjNjNWJ4V1ciLCJtYWMiOiIxNjdkNDVmN2FjYTdjOTQ0NzljMzJlOGY5N2JjNDAyM2ZlN2QyODhhYTFlOWI5ZTRmZDExMjNiYjQyZmM5YTU1IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:48:30 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImtJOCtMTFpoSDdNWHBwZUhCemczRlE9PSIsInZhbHVlIjoiREdxRE5tQUozOHNWUERTVzJuRFFMS01iTHRUdzltTkVTS0hmTWpIbStxZDZPb2l1WGM0V0lLb09OUWpVVFk2OEo2dnArRm1NcktRNzJCYXhKYjVMMkYvU3hyODJFenM1eWlOSStnTVkxRzhJcVFZdlMzbFR6YUVZRFkyaXlaWmsiLCJtYWMiOiJkYTE3MDEyM2ZlYzE4YjgzYTBiODQyMmQ1ZGNhODU0MjcyY2Q3ZGU1MmY5MGNmYWFmMmRlZWY5NzhhMWYzY2QxIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:48:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IjBiUEpKTlc2aThkN0pCUGY3R21JMnc9PSIsInZhbHVlIjoiZHloNis4amdya08ybWVCYWRCNUlTb1R4WjE1eG1SNjhSNGtQMnBXUWc1YkVVVDNUWVRHY0VTSG9mQURGWk1mT0JvSXI5c1l3aVFycXBVVHBhblBxblpqaVREdEV1V1Fzc0hIUG5NR091cHdxdVYwRUg3K3g2U3UzdjNjNWJ4V1ciLCJtYWMiOiIxNjdkNDVmN2FjYTdjOTQ0NzljMzJlOGY5N2JjNDAyM2ZlN2QyODhhYTFlOWI5ZTRmZDExMjNiYjQyZmM5YTU1IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:48:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-337379260\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-529740293 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">izyAMXVfTmNLIYwsWk1sKlgE1ngcloby6jsfh6pj</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://osool-b2g.test/user</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>21</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-529740293\", {\"maxDepth\":0})</script>\n"}}