{"__meta": {"id": "X60a93e33ff70bbb3718352a73f00645a", "datetime": "2025-07-28 16:49:15", "utime": 1753710555.150181, "method": "GET", "uri": "/dashboards/admin", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 8, "messages": [{"message": "[16:49:15] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753710555.068021, "xdebug_link": null, "collector": "log"}, {"message": "[16:49:15] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": 1753710555.093954, "xdebug_link": null, "collector": "log"}, {"message": "[16:49:15] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": 1753710555.094003, "xdebug_link": null, "collector": "log"}, {"message": "[16:49:15] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": 1753710555.094041, "xdebug_link": null, "collector": "log"}, {"message": "[16:49:15] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2418", "message_html": null, "is_string": false, "label": "warning", "time": 1753710555.139216, "xdebug_link": null, "collector": "log"}, {"message": "[16:49:15] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2478", "message_html": null, "is_string": false, "label": "warning", "time": 1753710555.139303, "xdebug_link": null, "collector": "log"}, {"message": "[16:49:15] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3619", "message_html": null, "is_string": false, "label": "warning", "time": 1753710555.140093, "xdebug_link": null, "collector": "log"}, {"message": "[16:49:15] LOG.info: Dashboard User Type Checked ====>super_admin", "message_html": null, "is_string": false, "label": "info", "time": 1753710555.141696, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753710554.555059, "end": 1753710555.150215, "duration": 0.5951559543609619, "duration_str": "595ms", "measures": [{"label": "Booting", "start": 1753710554.555059, "relative_start": 0, "end": 1753710555.043042, "relative_end": 1753710555.043042, "duration": 0.48798298835754395, "duration_str": "488ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753710555.043063, "relative_start": 0.4880039691925049, "end": 1753710555.150217, "relative_end": 2.1457672119140625e-06, "duration": 0.10715413093566895, "duration_str": "107ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 38077560, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET dashboards/admin", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Admin\\DashboardControllerNew@index", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "", "where": [], "as": "admin.dashboard", "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php&line=62\">\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:62-99</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00362, "accumulated_duration_str": "3.62ms", "statements": [{"sql": "select * from `users` where `id` = 21 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00362, "duration_str": "3.62ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": 1}, "count": 1}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"locale": "en", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/dashboards/admin\"\n]", "PHPDEBUGBAR_STACK_DATA": "array:1 [\n  \"X920ead6c4448a780abfc632071858cfd\" => null\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "21", "plain_user_password": "Tarqeem21"}, "request": {"path_info": "/dashboards/admin", "status_code": "<pre class=sf-dump id=sf-dump-571232319 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-571232319\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1473963804 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1473963804\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-385204923 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-385204923\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1800434754 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://osool-b2g.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IkhRNlFGcmc1dGF2NkZiMUtrL1RPblE9PSIsInZhbHVlIjoiMDZMRWs3VzlYbWJyVHBRZERMbG5jVHdEbzhtdGhPM2pFUGJybklIT1lTN3pOV25QQzZnQzlhVXB0aURuUHJ3MUxpbHJqckE2d0FHOWJCREFFVjFaOUdyMnNkdTBqVHpSNHRQQlJIMXZwdEVWWVljYzVaY2xYd0JWSDBiOEhVTjIiLCJtYWMiOiI3ZDk5NmI5OGE2M2E2YThkYzgzZGE4MWY5OWQ4ZWJhNWUxZTMxN2Q1MjhhMzUwZjM2MmM3OTJmY2RkNDIzMWEzIiwidGFnIjoiIn0%3D; osool_session=lLRALE4j9iJ7rHyRAFGh6VWxGVeCI5FyZjer3Izh</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1800434754\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-120249137 data-indent-pad=\"  \"><span class=sf-dump-note>array:40</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://osool-b2g.test/login</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IkhRNlFGcmc1dGF2NkZiMUtrL1RPblE9PSIsInZhbHVlIjoiMDZMRWs3VzlYbWJyVHBRZERMbG5jVHdEbzhtdGhPM2pFUGJybklIT1lTN3pOV25QQzZnQzlhVXB0aURuUHJ3MUxpbHJqckE2d0FHOWJCREFFVjFaOUdyMnNkdTBqVHpSNHRQQlJIMXZwdEVWWVljYzVaY2xYd0JWSDBiOEhVTjIiLCJtYWMiOiI3ZDk5NmI5OGE2M2E2YThkYzgzZGE4MWY5OWQ4ZWJhNWUxZTMxN2Q1MjhhMzUwZjM2MmM3OTJmY2RkNDIzMWEzIiwidGFnIjoiIn0%3D; osool_session=lLRALE4j9iJ7rHyRAFGh6VWxGVeCI5FyZjer3Izh</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52322</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/dashboards/admin</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/dashboards/admin</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753710554.5551</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753710554</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-120249137\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-955009223 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-955009223\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1516933258 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 13:49:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/workspace/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlNneFJ2MCtueUZDdTYwaUNNK29tR0E9PSIsInZhbHVlIjoiUkRuVjB6TGNDMkg2STloTFN0eWVjUHNuU1M3Smd4c0NPanJTTDk2RlptcStyZU9nN3VOZmlnazYvcUNFY21wYjd2cWNRUTNtMnRuanlDZGlsWlk3R2RWUlJ6bzczY1o4MFB6MlR1djJ2UC9wWGx1VWJYdkZZZTNzU01nT0dTRm4iLCJtYWMiOiI5NjhkMGE0MWIwZjM0M2I4NzQ4NmIzNDIyZDA3OGU3Y2FhMjdlMGYyMjI1ODE3NjM3NzY3NDUwNDU5ZTIwOGNiIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:49:15 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IlJZNnRHSkZkY3FXK05vSGdiemtTbkE9PSIsInZhbHVlIjoiNzR5cERlMzJkKzA3VjhsWTVlWkdPWnRBV0doRk1TdDd5YTI3YWtjVWJRcVU0RlNic2lHNnVvZXdNbytZSkVPMXBJVjFZN2RzNVJVK3FKK2Q3bXZnbUFSVGw4MlgybVFhbENvNFd1V1NuTnJIZ0llZ3NUczF4RnhXOHJTVnlFSHYiLCJtYWMiOiJkMDYzNzg1NzdlNDQ2NjhhNmJjNWY1MjJmZTEyMjU0YTgyMzk0NzQ0NTU3YjA3MjgxMTYwYjQwMTM2OGNmNTVlIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:49:15 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlNneFJ2MCtueUZDdTYwaUNNK29tR0E9PSIsInZhbHVlIjoiUkRuVjB6TGNDMkg2STloTFN0eWVjUHNuU1M3Smd4c0NPanJTTDk2RlptcStyZU9nN3VOZmlnazYvcUNFY21wYjd2cWNRUTNtMnRuanlDZGlsWlk3R2RWUlJ6bzczY1o4MFB6MlR1djJ2UC9wWGx1VWJYdkZZZTNzU01nT0dTRm4iLCJtYWMiOiI5NjhkMGE0MWIwZjM0M2I4NzQ4NmIzNDIyZDA3OGU3Y2FhMjdlMGYyMjI1ODE3NjM3NzY3NDUwNDU5ZTIwOGNiIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:49:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IlJZNnRHSkZkY3FXK05vSGdiemtTbkE9PSIsInZhbHVlIjoiNzR5cERlMzJkKzA3VjhsWTVlWkdPWnRBV0doRk1TdDd5YTI3YWtjVWJRcVU0RlNic2lHNnVvZXdNbytZSkVPMXBJVjFZN2RzNVJVK3FKK2Q3bXZnbUFSVGw4MlgybVFhbENvNFd1V1NuTnJIZ0llZ3NUczF4RnhXOHJTVnlFSHYiLCJtYWMiOiJkMDYzNzg1NzdlNDQ2NjhhNmJjNWY1MjJmZTEyMjU0YTgyMzk0NzQ0NTU3YjA3MjgxMTYwYjQwMTM2OGNmNTVlIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:49:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1516933258\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1916512454 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://osool-b2g.test/dashboards/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>X920ead6c4448a780abfc632071858cfd</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>21</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Tarqeem21</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1916512454\", {\"maxDepth\":0})</script>\n"}}