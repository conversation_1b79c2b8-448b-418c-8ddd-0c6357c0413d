{"__meta": {"id": "Xe653adfd7fa96226daec6b6781867bee", "datetime": "2025-07-28 16:44:54", "utime": 1753710294.086491, "method": "GET", "uri": "/workspace/adminProjectsListAjax?search_text=te&_=1753710288092", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 10, "messages": [{"message": "[16:44:53] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753710293.881606, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:53] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2418", "message_html": null, "is_string": false, "label": "warning", "time": 1753710293.952834, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:53] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2478", "message_html": null, "is_string": false, "label": "warning", "time": 1753710293.952925, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:53] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3619", "message_html": null, "is_string": false, "label": "warning", "time": 1753710293.953568, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:53] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710293.982551, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:53] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710293.995961, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:54] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710294.010726, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:54] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710294.037774, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:54] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710294.060647, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:54] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710294.076776, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753710293.230043, "end": 1753710294.086522, "duration": 0.8564791679382324, "duration_str": "856ms", "measures": [{"label": "Booting", "start": 1753710293.230043, "relative_start": 0, "end": 1753710293.847232, "relative_end": 1753710293.847232, "duration": 0.6171891689300537, "duration_str": "617ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753710293.847243, "relative_start": 0.6172001361846924, "end": 1753710294.086524, "relative_end": 1.9073486328125e-06, "duration": 0.23928093910217285, "duration_str": "239ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 37897376, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET workspace/adminProjectsListAjax/{id?}", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController@adminProjectListAjax", "as": "workspace.list_projects.ajax", "namespace": "App\\Http\\Controllers\\Admin\\Workspace", "prefix": "/workspace", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php&line=457\">\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:457-528</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.10355999999999999, "accumulated_duration_str": "104ms", "statements": [{"sql": "select * from `users` where `id` = 21 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00311, "duration_str": "3.11ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 3.003}, {"sql": "select count(*) as aggregate from (select `projects_details`.*, `user_projects`.`status` from `projects_details` left join `user_projects` on `user_projects`.`project_id` = `projects_details`.`id` where (`projects_details`.`project_name` LIKE '%te%' and `projects_details`.`deleted_at` is null and `projects_details`.`is_deleted` = '0') and `projects_details`.`user_id` = 21 and `projects_details`.`deleted_at` is null group by `projects_details`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["%te%", "0", "21"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 490}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00164, "duration_str": "1.64ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:490", "connection": "osool_test_db", "start_percent": 3.003, "width_percent": 1.584}, {"sql": "select count(*) as aggregate from `projects_details` left join `users` on `users`.`id` = `projects_details`.`user_id` where ((`projects_details`.`project_name` LIKE '%te%' and `projects_details`.`deleted_at` is null and `projects_details`.`is_deleted` = 0) or (`projects_details`.`project_name_ar` LIKE '%te%' and `projects_details`.`deleted_at` is null and `projects_details`.`is_deleted` = 0) and `projects_details`.`deleted_at` is null) and `projects_details`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["%te%", "0", "%te%", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 499}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008900000000000001, "duration_str": "890μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:499", "connection": "osool_test_db", "start_percent": 4.587, "width_percent": 0.859}, {"sql": "select `projects_details`.*, `users`.`name` from `projects_details` left join `users` on `users`.`id` = `projects_details`.`user_id` where ((`projects_details`.`project_name` LIKE '%te%' and `projects_details`.`deleted_at` is null and `projects_details`.`is_deleted` = 0) or (`projects_details`.`project_name_ar` LIKE '%te%' and `projects_details`.`deleted_at` is null and `projects_details`.`is_deleted` = 0) and `projects_details`.`deleted_at` is null) and `projects_details`.`deleted_at` is null order by `id` desc limit 6 offset 0", "type": "query", "params": [], "bindings": ["%te%", "0", "%te%", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 499}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:499", "connection": "osool_test_db", "start_percent": 5.446, "width_percent": 0.686}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 205 limit 1", "type": "query", "params": [], "bindings": ["admin", "205"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0134, "duration_str": "13.4ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 6.132, "width_percent": 12.939}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 204 limit 1", "type": "query", "params": [], "bindings": ["admin", "204"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.011689999999999999, "duration_str": "11.69ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 19.071, "width_percent": 11.288}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 203 limit 1", "type": "query", "params": [], "bindings": ["admin", "203"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01331, "duration_str": "13.31ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 30.359, "width_percent": 12.852}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 201 limit 1", "type": "query", "params": [], "bindings": ["admin", "201"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.023649999999999997, "duration_str": "23.65ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 43.212, "width_percent": 22.837}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 200 limit 1", "type": "query", "params": [], "bindings": ["admin", "200"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.02112, "duration_str": "21.12ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 66.049, "width_percent": 20.394}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 199 limit 1", "type": "query", "params": [], "bindings": ["admin", "199"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.014039999999999999, "duration_str": "14.04ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 86.443, "width_percent": 13.557}]}, "models": {"data": {"App\\Models\\ProjectsDetails": 6, "App\\Models\\User": 1}, "count": 7}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"locale": "en", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "MoHVz4Tx7nVo7GO6J30zRNXQMrc7mCwjsdZxDGeF", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/_debugbar/open?id=Xe7df636e5051a305e6173977a1ba68af&op=get\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "21", "plain_user_password": "123456", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/workspace/adminProjectsListAjax", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1930455725 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search_text</span>\" => \"<span class=sf-dump-str title=\"2 characters\">te</span>\"\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1753710288092</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1930455725\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1327271132 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search_text</span>\" => \"<span class=sf-dump-str title=\"2 characters\">te</span>\"\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1753710288092</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1327271132\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1969967794 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/workspace/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6Ik5tQWhUNHdsVjZvNDJRZG9BQU5BVUE9PSIsInZhbHVlIjoiTjd2TGtCQ1orQXp0cTJtU2R1L1E5V0k1b2pGd3phOGlYTkpiNXdZeDMrdW1pR3d4Q054d2k4ckU0ejRtM0ltb0pHMmxHMW9jckp2WDllUUNRUVRLTlh1QlNveG9LWmxBOXdJMjdqaEdwVnBvU0UwdWFGaWJOaVVQMlVySVZmbDUiLCJtYWMiOiJjOGQ2OTU0Njc3NTExODg4MGI4YzdkNzA2ZDE4MjVlMTBmZGE2NTFkOTgxZmQzNDBkMzY0ODQ0OTkzMzY5ZTM3IiwidGFnIjoiIn0%3D; osool_session=6DgKCOs6hon8igN9783Ix5l9wKt1SeDcjlfyZhcD</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1969967794\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-2069506685 data-indent-pad=\"  \"><span class=sf-dump-note>array:40</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/workspace/projects</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6Ik5tQWhUNHdsVjZvNDJRZG9BQU5BVUE9PSIsInZhbHVlIjoiTjd2TGtCQ1orQXp0cTJtU2R1L1E5V0k1b2pGd3phOGlYTkpiNXdZeDMrdW1pR3d4Q054d2k4ckU0ejRtM0ltb0pHMmxHMW9jckp2WDllUUNRUVRLTlh1QlNveG9LWmxBOXdJMjdqaEdwVnBvU0UwdWFGaWJOaVVQMlVySVZmbDUiLCJtYWMiOiJjOGQ2OTU0Njc3NTExODg4MGI4YzdkNzA2ZDE4MjVlMTBmZGE2NTFkOTgxZmQzNDBkMzY0ODQ0OTkzMzY5ZTM3IiwidGFnIjoiIn0%3D; osool_session=6DgKCOs6hon8igN9783Ix5l9wKt1SeDcjlfyZhcD</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51849</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"32 characters\">/workspace/adminProjectsListAjax</span>\"\n  \"<span class=sf-dump-key>REDIRECT_QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"30 characters\">search_text=te&amp;_=1753710288092</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"30 characters\">search_text=te&amp;_=1753710288092</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"63 characters\">/workspace/adminProjectsListAjax?search_text=te&amp;_=1753710288092</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753710293.23</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753710293</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2069506685\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2046086217 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MoHVz4Tx7nVo7GO6J30zRNXQMrc7mCwjsdZxDGeF</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2046086217\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-184986239 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 13:44:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImU0cnBNRFlBOWdJVW00c2d3RHluM0E9PSIsInZhbHVlIjoibXJnMDF0S3ZuQ2x1QTJLQ2kzb0tIQXBhRi9TVGV2a1VZNE5tOWNPaGRqdGZwS0FqZlE1SGJPd2kxb2laTEN2WlU1YVZyckRSUlE5blVSYnFpenZIWnZld1o5VU1KZ1p1cGpTOG43bUNEN01qVlNOTEFTLzZMMjVGMlcvd1pBQXciLCJtYWMiOiI0ZjFlNGE4MzI0NjUzZjhiZGU4YmIzNWIzMzU0NTkwNzQzYjE4NzNjMTU2OTJkMTEyMzQ4NTM3MmQ4OTVmMjE5IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:44:54 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6ImlhWTRlNGQ2RGRVTUlsUUx6RTU4OHc9PSIsInZhbHVlIjoibCtJNSt2WGlveUoyTThwNVcwa1NrTEcrTGkwbXM0VVMrNDQ4YitTV05FdnVEWmtlZGJTVGtCVGt5V0lGVkNFRmJsTHNKWkxEdDYrekhTL1JIZ092U2V5emxaMFhudWZXNllLSi84MHdkUmlJUVNPKzUzSDZoMEJQNzR4TmJGZVkiLCJtYWMiOiIwNjU1ODU5YTVjOWRhZGZlZjE0NmZmNjY5MGFhNTVkN2YwMTg2YWMzNDdkOTFhZmU0N2FjMjdkNThhY2Y5MzNiIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:44:54 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImU0cnBNRFlBOWdJVW00c2d3RHluM0E9PSIsInZhbHVlIjoibXJnMDF0S3ZuQ2x1QTJLQ2kzb0tIQXBhRi9TVGV2a1VZNE5tOWNPaGRqdGZwS0FqZlE1SGJPd2kxb2laTEN2WlU1YVZyckRSUlE5blVSYnFpenZIWnZld1o5VU1KZ1p1cGpTOG43bUNEN01qVlNOTEFTLzZMMjVGMlcvd1pBQXciLCJtYWMiOiI0ZjFlNGE4MzI0NjUzZjhiZGU4YmIzNWIzMzU0NTkwNzQzYjE4NzNjMTU2OTJkMTEyMzQ4NTM3MmQ4OTVmMjE5IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:44:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6ImlhWTRlNGQ2RGRVTUlsUUx6RTU4OHc9PSIsInZhbHVlIjoibCtJNSt2WGlveUoyTThwNVcwa1NrTEcrTGkwbXM0VVMrNDQ4YitTV05FdnVEWmtlZGJTVGtCVGt5V0lGVkNFRmJsTHNKWkxEdDYrekhTL1JIZ092U2V5emxaMFhudWZXNllLSi84MHdkUmlJUVNPKzUzSDZoMEJQNzR4TmJGZVkiLCJtYWMiOiIwNjU1ODU5YTVjOWRhZGZlZjE0NmZmNjY5MGFhNTVkN2YwMTg2YWMzNDdkOTFhZmU0N2FjMjdkNThhY2Y5MzNiIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:44:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-184986239\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1102434906 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MoHVz4Tx7nVo7GO6J30zRNXQMrc7mCwjsdZxDGeF</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"80 characters\">http://osool-b2g.test/_debugbar/open?id=Xe7df636e5051a305e6173977a1ba68af&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>21</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1102434906\", {\"maxDepth\":0})</script>\n"}}