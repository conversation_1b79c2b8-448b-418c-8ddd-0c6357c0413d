{"__meta": {"id": "X0f1cc55e8921239003aa86ca34b61979", "datetime": "2025-07-28 16:48:56", "utime": **********.675308, "method": "POST", "uri": "/user/update-profile-password", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 8, "messages": [{"message": "[16:48:56] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.487102, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:56] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.525717, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:56] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.525764, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:56] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.525802, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:56] LOG.warning: Optional parameter $serviceProviderId declared before required parameter $search is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\UserTrait.php on line 328", "message_html": null, "is_string": false, "label": "warning", "time": **********.527537, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:56] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2418", "message_html": null, "is_string": false, "label": "warning", "time": **********.585418, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:56] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2478", "message_html": null, "is_string": false, "label": "warning", "time": **********.585495, "xdebug_link": null, "collector": "log"}, {"message": "[16:48:56] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3619", "message_html": null, "is_string": false, "label": "warning", "time": **********.58598, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753710535.999913, "end": **********.675357, "duration": 0.6754441261291504, "duration_str": "675ms", "measures": [{"label": "Booting", "start": 1753710535.999913, "relative_start": 0, "end": **********.463452, "relative_end": **********.463452, "duration": 0.46353912353515625, "duration_str": "464ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.463469, "relative_start": 0.46355605125427246, "end": **********.675359, "relative_end": 1.9073486328125e-06, "duration": 0.21188998222351074, "duration_str": "212ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 39308768, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST user/update-profile-password", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Admin\\User\\UserControllerNew@update_profile_password", "as": "users.profile.update3", "namespace": "App\\Http\\Controllers\\Admin\\User", "prefix": "/user", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php&line=3345\">\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php:3345-3354</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0084, "accumulated_duration_str": "8.4ms", "statements": [{"sql": "select * from `users` where `id` = 21 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00458, "duration_str": "4.58ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 54.524}, {"sql": "update `users` set `password` = '$2y$10$JCvJmAzGnnOeoh9po8kYju4db9GhmC.s6.ue6cmFhafhv6MgdUINu', `users`.`modified_at` = '2025-07-28 16:48:56' where `id` = 21 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["$2y$10$JCvJmAzGnnOeoh9po8kYju4db9GhmC.s6.ue6cmFhafhv6MgdUINu", "2025-07-28 16:48:56", "21"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php", "line": 3352}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00382, "duration_str": "3.82ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php:3352", "connection": "osool_test_db", "start_percent": 54.524, "width_percent": 45.476}]}, "models": {"data": {"App\\Models\\User": 1}, "count": 1}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"locale": "en", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "_token": "izyAMXVfTmNLIYwsWk1sKlgE1ngcloby6jsfh6pj", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/_debugbar/open?id=Xde0f62ba59c4a3d13edbf480516ef6fb&op=get\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "21", "plain_user_password": "123456", "PHPDEBUGBAR_STACK_DATA": "[]", "success": "Profile successfully updated."}, "request": {"path_info": "/user/update-profile-password", "status_code": "<pre class=sf-dump id=sf-dump-640427257 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-640427257\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1820272823 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1820272823\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-569051627 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">izyAMXVfTmNLIYwsWk1sKlgE1ngcloby6jsfh6pj</span>\"\n  \"<span class=sf-dump-key>old_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Tarqeem21</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-569051627\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">386</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarydI34i3WcY7Q4SjFW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://osool-b2g.test/user</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IkJxZ3VuaHEwQ09UbDlyMFlSTHgvL1E9PSIsInZhbHVlIjoiWTBKMEovSzF6NVY1NEVJMjhOMGx6Yk1wdHFGUWRDd0dlUjluemkrWkxILy90Yi8yYWJWWGowMkJUZGorL215dVhiRTRVVjlXamVrSVJBUlNRQkhCUFl4ak9KTTl5ZFZXVTFDaUxhWDlxMXB6aFVnRk42YlJvOVVDNXRGblJEa3UiLCJtYWMiOiI2NTkyM2YxZjFhMjgyYTE4MTg4Y2Y5NGQ5YmNlOTFlYjQwNTYxM2I3YzU3MDZiNDI0YzVhMmVjNzRlMDUyNDYzIiwidGFnIjoiIn0%3D; osool_session=mZeQcLLAzObrRyDBAKgwVjo4mt5zqjm6EQwhNms5</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-80613245 data-indent-pad=\"  \"><span class=sf-dump-note>array:43</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">386</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarydI34i3WcY7Q4SjFW</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://osool-b2g.test/user</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IkJxZ3VuaHEwQ09UbDlyMFlSTHgvL1E9PSIsInZhbHVlIjoiWTBKMEovSzF6NVY1NEVJMjhOMGx6Yk1wdHFGUWRDd0dlUjluemkrWkxILy90Yi8yYWJWWGowMkJUZGorL215dVhiRTRVVjlXamVrSVJBUlNRQkhCUFl4ak9KTTl5ZFZXVTFDaUxhWDlxMXB6aFVnRk42YlJvOVVDNXRGblJEa3UiLCJtYWMiOiI2NTkyM2YxZjFhMjgyYTE4MTg4Y2Y5NGQ5YmNlOTFlYjQwNTYxM2I3YzU3MDZiNDI0YzVhMmVjNzRlMDUyNDYzIiwidGFnIjoiIn0%3D; osool_session=mZeQcLLAzObrRyDBAKgwVjo4mt5zqjm6EQwhNms5</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52279</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/user/update-profile-password</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/user/update-profile-password</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753710535.9999</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753710535</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-80613245\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1091039176 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">izyAMXVfTmNLIYwsWk1sKlgE1ngcloby6jsfh6pj</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1091039176\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2003567597 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 13:48:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://osool-b2g.test/user</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImZDKzJnTnVkR3h3YXYrdmxGcHJ3Y0E9PSIsInZhbHVlIjoienZMY2oxdG5xeUQ1bmJLRUJtWFdPdXFJNkg2aFNJbnprWkhIWVZuRWZDamF3VUVzbFUySzhiVGdqMWZwZGtOUWZ4Qm14dEpzQ2ZrSTVZNUNyRFV2RTZsY2FDRk1FRVI1VEloZlptalNDWkRyY1NZdmdwZnRPeTcyVkQ4R2RzSGYiLCJtYWMiOiIxYWVmZWZjMTM3NzA3ZTlhZmE4NGU4ZDZkZmE5MDM1OGI3ODQ3YzZiNGFlOTg0MWQ3NWJhM2VjMmY3MTkxNjZkIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:48:56 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IkJaOUFVaHJpQ25sSUM4MDdoNFQ5cEE9PSIsInZhbHVlIjoiWURKSForMWlRMC9CQklqS1daZUlNeHZPMDg1R3VDYzQ0ak9CQVpzWjVCRTNpY1lvNFQxY1FsRTNUYmpZZFJJOTVxQXVqdGhCUDM4eldFWWFLVGN4M0I5ZHQ4SlYyWWNaQzZKbHBlRWcyaGkwTURLQWZnNndmeGZiaWVVdUc1aG8iLCJtYWMiOiI4MjJkYTIzYWI5MWJmMjEzZjI2MzFlNmRjMGFjZjI2M2JhZDQ1MDVhZTIyZTViOTg0NGM2Yjg0MWE1NjliZWYxIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:48:56 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImZDKzJnTnVkR3h3YXYrdmxGcHJ3Y0E9PSIsInZhbHVlIjoienZMY2oxdG5xeUQ1bmJLRUJtWFdPdXFJNkg2aFNJbnprWkhIWVZuRWZDamF3VUVzbFUySzhiVGdqMWZwZGtOUWZ4Qm14dEpzQ2ZrSTVZNUNyRFV2RTZsY2FDRk1FRVI1VEloZlptalNDWkRyY1NZdmdwZnRPeTcyVkQ4R2RzSGYiLCJtYWMiOiIxYWVmZWZjMTM3NzA3ZTlhZmE4NGU4ZDZkZmE5MDM1OGI3ODQ3YzZiNGFlOTg0MWQ3NWJhM2VjMmY3MTkxNjZkIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:48:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IkJaOUFVaHJpQ25sSUM4MDdoNFQ5cEE9PSIsInZhbHVlIjoiWURKSForMWlRMC9CQklqS1daZUlNeHZPMDg1R3VDYzQ0ak9CQVpzWjVCRTNpY1lvNFQxY1FsRTNUYmpZZFJJOTVxQXVqdGhCUDM4eldFWWFLVGN4M0I5ZHQ4SlYyWWNaQzZKbHBlRWcyaGkwTURLQWZnNndmeGZiaWVVdUc1aG8iLCJtYWMiOiI4MjJkYTIzYWI5MWJmMjEzZjI2MzFlNmRjMGFjZjI2M2JhZDQ1MDVhZTIyZTViOTg0NGM2Yjg0MWE1NjliZWYxIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:48:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2003567597\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1283844537 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">izyAMXVfTmNLIYwsWk1sKlgE1ngcloby6jsfh6pj</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"80 characters\">http://osool-b2g.test/_debugbar/open?id=Xde0f62ba59c4a3d13edbf480516ef6fb&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>21</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Profile successfully updated.</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1283844537\", {\"maxDepth\":0})</script>\n"}}