{"__meta": {"id": "X99fec0ebe06f929f7c60cbf515b6bf11", "datetime": "2025-07-28 16:55:20", "utime": **********.339805, "method": "GET", "uri": "/user/users-list", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 11, "messages": [{"message": "[16:55:19] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.96451, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:20] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.010807, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:20] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.010887, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:20] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.010928, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:20] LOG.warning: Optional parameter $serviceProviderId declared before required parameter $search is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\UserTrait.php on line 328", "message_html": null, "is_string": false, "label": "warning", "time": **********.013594, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:20] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2418", "message_html": null, "is_string": false, "label": "warning", "time": **********.073993, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:20] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2478", "message_html": null, "is_string": false, "label": "warning", "time": **********.074068, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:20] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3619", "message_html": null, "is_string": false, "label": "warning", "time": **********.075018, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:20] LOG.warning: Optional parameter $filters declared before required parameter $userServiceProvider is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\WorkOrdersTrait.php on line 3418", "message_html": null, "is_string": false, "label": "warning", "time": **********.172961, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:20] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 637", "message_html": null, "is_string": false, "label": "warning", "time": **********.252304, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:20] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\f8d18d6f63e9bf36e98d3bb1b82c9e7d86b4c71c.php on line 3", "message_html": null, "is_string": false, "label": "warning", "time": **********.285228, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.372104, "end": **********.339843, "duration": 0.9677391052246094, "duration_str": "968ms", "measures": [{"label": "Booting", "start": **********.372104, "relative_start": 0, "end": **********.93999, "relative_end": **********.93999, "duration": 0.5678861141204834, "duration_str": "568ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.94001, "relative_start": 0.5679061412811279, "end": **********.339845, "relative_end": 1.9073486328125e-06, "duration": 0.39983487129211426, "duration_str": "400ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 46947832, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 11, "templates": [{"name": "applications.admin.user.list-row-view (\\resources\\views\\applications\\admin\\user\\list-row-view.blade.php)", "param_count": 14, "params": ["data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.app (\\resources\\views\\layouts\\app.blade.php)", "param_count": 21, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "value", "key", "loop"], "type": "blade"}, {"name": "layouts.partials._styles (\\resources\\views\\layouts\\partials\\_styles.blade.php)", "param_count": 21, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "value", "key", "loop"], "type": "blade"}, {"name": "layouts.partials._header (\\resources\\views\\layouts\\partials\\_header.blade.php)", "param_count": 21, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "value", "key", "loop"], "type": "blade"}, {"name": "layouts.partials._top_menu (\\resources\\views\\layouts\\partials\\_top_menu.blade.php)", "param_count": 21, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "value", "key", "loop"], "type": "blade"}, {"name": "livewire.notifications.messages-notifications-list (\\resources\\views\\livewire\\notifications\\messages-notifications-list.blade.php)", "param_count": 23, "params": ["chatList", "errors", "_instance", "workspaceSlug", "totalUnreadNotifications", "previousUnreadCount", "newList", "list", "slugs", "userId", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.notifications.new-notifications-list-top-nav (\\resources\\views\\livewire\\notifications\\new-notifications-list-top-nav.blade.php)", "param_count": 28, "params": ["list", "totalUnreadNotifications", "errors", "_instance", "user", "perPage", "assignedAsset", "contractsIds", "accessBuildingsIds", "currentDate", "currentDateTime", "readyToLoad", "configOciLink", "ociLink", "selectedLanguage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.menu.aside-nav-list (\\resources\\views\\livewire\\menu\\aside-nav-list.blade.php)", "param_count": 27, "params": ["userPrivilegesAside", "user", "hasViewPrivilege", "errors", "_instance", "has<PERSON>dmin", "projectId", "project", "workOrderMenuItemColor", "flagWorkorderSidebarMenu", "userPrivileges", "closedWorkOrderCount", "maintenanceRequestCount", "vendorRegistrationApplicationRequests", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.partials._footer (\\resources\\views\\layouts\\partials\\_footer.blade.php)", "param_count": 24, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "value", "key", "loop", "html", "url", "segments"], "type": "blade"}, {"name": "layouts.partials.check_crm_session (\\resources\\views\\layouts\\partials\\check_crm_session.blade.php)", "param_count": 24, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "value", "key", "loop", "html", "url", "segments"], "type": "blade"}, {"name": "layouts.partials._scripts (\\resources\\views\\layouts\\partials\\_scripts.blade.php)", "param_count": 24, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "value", "key", "loop", "html", "url", "segments"], "type": "blade"}]}, "route": {"uri": "GET user/users-list/{availability_request_id?}/{notification_read?}/{notification_id?}", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Admin\\User\\UserControllerNew@list", "as": "users.list", "namespace": "App\\Http\\Controllers\\Admin\\User", "prefix": "/user", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php&line=112\">\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php:112-250</a>"}, "queries": {"nb_statements": 16, "nb_failed_statements": 0, "accumulated_duration": 0.05614999999999999, "accumulated_duration_str": "56.15ms", "statements": [{"sql": "select * from `users` where `id` = 6942 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6942"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00414, "duration_str": "4.14ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 7.373}, {"sql": "select count(*) as aggregate from `users` left join `service_providers` on `service_providers`.`id` = `users`.`service_provider` where ( `service_providers`.`global_sp` = '0' or `service_providers`.`global_sp` IS NULL )  and `user_type` != 'osool_admin' and `user_type` != 'tenant' and `user_type` != 'admin' and `user_type` != 'super_admin' and (`users`.`deleted_at` is null and `users`.`is_deleted` = 'no') and `users`.`project_user_id` = 6942 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["osool_admin", "tenant", "admin", "super_admin", "no", "6942"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php", "line": 175}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0086, "duration_str": "8.6ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php:175", "connection": "osool_test_db", "start_percent": 7.373, "width_percent": 15.316}, {"sql": "select * from `worker_availability_request_reason_types`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php", "line": 248}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00141, "duration_str": "1.41ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php:248", "connection": "osool_test_db", "start_percent": 22.689, "width_percent": 2.511}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 191 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["191"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Providers\\AsideViewComposerServiceProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.00127, "duration_str": "1.27ms", "stmt_id": "\\app\\Providers\\AsideViewComposerServiceProvider.php:59", "connection": "osool_test_db", "start_percent": 25.2, "width_percent": 2.262}, {"sql": "select * from `release_notes` where `store_status` = 1 and `release_notes`.`deleted_at` is null group by `version`", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 3048}, {"index": 15, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 51}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.0015400000000000001, "duration_str": "1.54ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:3048", "connection": "osool_test_db", "start_percent": 27.462, "width_percent": 2.743}, {"sql": "select * from `crm_user` where `crm_user`.`user_id` = 6942 and `crm_user`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["6942"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "9740534860d5c11a9cf9e73e8939e4083f82ba1d", "line": 122}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 122}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "view::9740534860d5c11a9cf9e73e8939e4083f82ba1d:122", "connection": "osool_test_db", "start_percent": 30.205, "width_percent": 1.229}, {"sql": "select `name`, `name_ar` from `user_type` where `slug` = 'admin' limit 1", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1929}, {"index": 14, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 161}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00123, "duration_str": "1.23ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1929", "connection": "osool_test_db", "start_percent": 31.434, "width_percent": 2.191}, {"sql": "select `id`, `project_image`, `use_beneficiary_module`, `use_tenant_module`, `benificiary_status`, `tenant_status`, `project_name`, `project_name_ar`, `use_crm_module` from `projects_details` where `id` = 191 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["191"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\ProjectDetailTrait.php", "line": 11}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 128}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 71}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00109, "duration_str": "1.09ms", "stmt_id": "\\app\\Http\\Traits\\ProjectDetailTrait.php:11", "connection": "osool_test_db", "start_percent": 33.624, "width_percent": 1.941}, {"sql": "select * from `work_orders` where `project_user_id` = 6942", "type": "query", "params": [], "bindings": ["6942"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\RequestedItemService.php", "line": 21}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 148}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 73}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.02404, "duration_str": "24.04ms", "stmt_id": "\\app\\Services\\RequestedItemService.php:21", "connection": "osool_test_db", "start_percent": 35.565, "width_percent": 42.814}, {"sql": "select count(*) as aggregate from `service_provider_missing_items_requests` where `status` = 'requested' and `work_order_id` in (188977, 188978, 188980, 188981, 188982, 188983, 188984, 188985, 195328, 195329, 195330, 196219, 196220, 196221, 196222, 196223, 196224, 196225, 196226, 196227, 196228, 196229, 196230, 196231, 196232, 196233, 196234, 196235, 196236, 196237, 196238, 196239, 196240, 196241, 196242, 196243, 196244, 196245, 196246, 196247, 196248, 196249, 196250, 196251, 196252, 196253, 196254, 196255, 196256, 196257, 196258, 196259, 196260, 196261, 196262, 196263, 196264, 196265, 196266, 196267, 196268, 196269, 196270, 196271, 196272, 196273, 196274, 196275, 196276, 196277, 196278, 196279, 196280, 196281, 196282, 196283, 196284, 196285, 196286, 196287, 196288, 196289, 196290, 196291, 196292, 196293, 196294, 196295, 196296, 196297, 196298, 196299, 196300, 196301, 196302, 196303, 196304, 196305, 196306, 196307, 196308, 196309, 196310, 196311, 196312, 196313, 196314, 196315, 196316, 196317, 196318, 196319, 196320, 196321, 196322, 196323, 196324, 196325, 196326, 196327, 196328, 196329, 196330, 196331, 196332, 196333, 196334, 196335, 196336, 196337, 196338, 196339, 196340, 196341, 196342, 196343, 196344, 196345, 196346, 196347, 196348, 196349, 196350, 196351, 196352, 196353, 196354, 196355, 196356, 196357, 196358, 196359, 196360, 196361, 196362, 196363, 196364, 196365, 196366, 196367, 196368, 196369, 196370, 196371, 196372, 196373, 196374, 196375, 196376, 196377, 196378, 196379, 196380, 196381, 196382, 196383, 196384, 196385, 196386, 196387, 196388, 196389, 196390, 196391, 196392, 196393, 196394, 196395, 196396, 196397, 196398, 196399, 196400, 196401, 196402, 196403, 196404, 196405, 196406, 196407, 196408, 196409, 196410, 196411, 196412, 196413, 196414, 196415, 196416, 196417, 196418, 196419, 196420, 196421, 196422, 196423, 196424, 196425, 196426, 196427, 196428, 196429, 196430, 196431, 196432, 196433, 196434, 196435, 196436, 196437, 196438, 196439, 196440, 196441, 196442, 196443, 196444, 196445, 196446, 196447, 196448, 196449, 196450, 196451, 196452, 196453, 196454, 196455, 196456, 196457, 196458, 196459, 196460, 196461, 196502, 196503, 196504, 196505, 196506, 196507, 196508, 196509, 196510, 196511, 196512, 196513, 196514, 196515, 196516, 196517, 196518, 196519, 196520, 196521, 196522, 196523, 196524, 196525, 196526, 196527, 196528, 196529, 196530, 196531, 196532, 196533, 196534, 196535, 196536, 196537, 196538, 196539, 196540, 196541, 196542, 196543, 196544, 196545, 196546, 196547, 196548, 196549, 196550, 196551, 196552, 196553, 196554, 196555, 196556, 196557, 196558, 196559, 196560, 196561, 196562, 196563, 196564, 196565, 196566, 196567, 196568, 196569, 196570, 196571, 196572, 196573, 196574, 196575, 196576, 196577, 196578, 196579, 196580, 196581, 196583, 196584, 196585, 196586, 196587, 196588, 196589, 196590, 196591, 196592, 196593, 196594, 196595, 196596, 196598, 196600, 196601, 196602, 196603, 196604, 196605, 196606, 196607, 196608, 196609, 196610, 196623, 196624, 196625, 196626, 196627, 196628, 196629, 196630, 196631, 196632, 196633, 196634, 196635, 196636, 196637, 196638, 196639, 196640)", "type": "query", "params": [], "bindings": ["requested", "188977", "188978", "188980", "188981", "188982", "188983", "188984", "188985", "195328", "195329", "195330", "196219", "196220", "196221", "196222", "196223", "196224", "196225", "196226", "196227", "196228", "196229", "196230", "196231", "196232", "196233", "196234", "196235", "196236", "196237", "196238", "196239", "196240", "196241", "196242", "196243", "196244", "196245", "196246", "196247", "196248", "196249", "196250", "196251", "196252", "196253", "196254", "196255", "196256", "196257", "196258", "196259", "196260", "196261", "196262", "196263", "196264", "196265", "196266", "196267", "196268", "196269", "196270", "196271", "196272", "196273", "196274", "196275", "196276", "196277", "196278", "196279", "196280", "196281", "196282", "196283", "196284", "196285", "196286", "196287", "196288", "196289", "196290", "196291", "196292", "196293", "196294", "196295", "196296", "196297", "196298", "196299", "196300", "196301", "196302", "196303", "196304", "196305", "196306", "196307", "196308", "196309", "196310", "196311", "196312", "196313", "196314", "196315", "196316", "196317", "196318", "196319", "196320", "196321", "196322", "196323", "196324", "196325", "196326", "196327", "196328", "196329", "196330", "196331", "196332", "196333", "196334", "196335", "196336", "196337", "196338", "196339", "196340", "196341", "196342", "196343", "196344", "196345", "196346", "196347", "196348", "196349", "196350", "196351", "196352", "196353", "196354", "196355", "196356", "196357", "196358", "196359", "196360", "196361", "196362", "196363", "196364", "196365", "196366", "196367", "196368", "196369", "196370", "196371", "196372", "196373", "196374", "196375", "196376", "196377", "196378", "196379", "196380", "196381", "196382", "196383", "196384", "196385", "196386", "196387", "196388", "196389", "196390", "196391", "196392", "196393", "196394", "196395", "196396", "196397", "196398", "196399", "196400", "196401", "196402", "196403", "196404", "196405", "196406", "196407", "196408", "196409", "196410", "196411", "196412", "196413", "196414", "196415", "196416", "196417", "196418", "196419", "196420", "196421", "196422", "196423", "196424", "196425", "196426", "196427", "196428", "196429", "196430", "196431", "196432", "196433", "196434", "196435", "196436", "196437", "196438", "196439", "196440", "196441", "196442", "196443", "196444", "196445", "196446", "196447", "196448", "196449", "196450", "196451", "196452", "196453", "196454", "196455", "196456", "196457", "196458", "196459", "196460", "196461", "196502", "196503", "196504", "196505", "196506", "196507", "196508", "196509", "196510", "196511", "196512", "196513", "196514", "196515", "196516", "196517", "196518", "196519", "196520", "196521", "196522", "196523", "196524", "196525", "196526", "196527", "196528", "196529", "196530", "196531", "196532", "196533", "196534", "196535", "196536", "196537", "196538", "196539", "196540", "196541", "196542", "196543", "196544", "196545", "196546", "196547", "196548", "196549", "196550", "196551", "196552", "196553", "196554", "196555", "196556", "196557", "196558", "196559", "196560", "196561", "196562", "196563", "196564", "196565", "196566", "196567", "196568", "196569", "196570", "196571", "196572", "196573", "196574", "196575", "196576", "196577", "196578", "196579", "196580", "196581", "196583", "196584", "196585", "196586", "196587", "196588", "196589", "196590", "196591", "196592", "196593", "196594", "196595", "196596", "196598", "196600", "196601", "196602", "196603", "196604", "196605", "196606", "196607", "196608", "196609", "196610", "196623", "196624", "196625", "196626", "196627", "196628", "196629", "196630", "196631", "196632", "196633", "196634", "196635", "196636", "196637", "196638", "196639", "196640"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\RequestedItemService.php", "line": 26}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 148}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 73}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.0020800000000000003, "duration_str": "2.08ms", "stmt_id": "\\app\\Services\\RequestedItemService.php:26", "connection": "osool_test_db", "start_percent": 78.379, "width_percent": 3.704}, {"sql": "select `id`, `created_at` from `users` where `project_id` = 191 and `user_type` = 'admin' and `status` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["191", "admin", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\UserTrait.php", "line": 256}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Traits\\UserTrait.php", "line": 267}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 158}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 74}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.00379, "duration_str": "3.79ms", "stmt_id": "\\app\\Http\\Traits\\UserTrait.php:256", "connection": "osool_test_db", "start_percent": 82.084, "width_percent": 6.75}, {"sql": "select exists(select * from `projects_details` where `id` = 191 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["191", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 897}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_test_db", "start_percent": 88.833, "width_percent": 1.354}, {"sql": "select exists(select * from `crm_user` where `user_id` = 6942) as `exists`", "type": "query", "params": [], "bindings": ["6942"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 897}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_test_db", "start_percent": 90.187, "width_percent": 0.855}, {"sql": "select `id` from `users` where `project_id` = 191 and `user_type` = 'admin' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["191", "admin", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 928}, {"index": 14, "namespace": "view", "name": "f8d18d6f63e9bf36e98d3bb1b82c9e7d86b4c71c", "line": 13}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.0036, "duration_str": "3.6ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:928", "connection": "osool_test_db", "start_percent": 91.042, "width_percent": 6.411}, {"sql": "select exists(select * from `projects_details` where `id` = 191 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["191", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 183}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_test_db", "start_percent": 97.453, "width_percent": 1.211}, {"sql": "select exists(select * from `crm_user` where `user_id` = 6942) as `exists`", "type": "query", "params": [], "bindings": ["6942"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 183}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_test_db", "start_percent": 98.664, "width_percent": 1.336}]}, "models": {"data": {"App\\Models\\WorkOrders": 378, "App\\Models\\CrmUser": 1, "App\\Models\\ReleaseNotes": 2, "App\\Models\\ProjectsDetails": 2, "App\\Models\\WorkerAvailabilityRequestReasonType": 6, "App\\Models\\User": 2}, "count": 391}, "livewire": {"data": {"notifications.messages-notifications-list #mDgWiN3kNwhwbG8nvpoR": "array:5 [\n  \"data\" => array:7 [\n    \"workspaceSlug\" => \"testing23\"\n    \"totalUnreadNotifications\" => 0\n    \"previousUnreadCount\" => 0\n    \"newList\" => null\n    \"list\" => []\n    \"slugs\" => array:3 [\n      0 => \"facebook\"\n      1 => \"whatsapp\"\n      2 => \"instagram\"\n    ]\n    \"userId\" => null\n  ]\n  \"name\" => \"notifications.messages-notifications-list\"\n  \"view\" => \"livewire.notifications.messages-notifications-list\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\MessagesNotificationsList\"\n  \"id\" => \"mDgWiN3kNwhwbG8nvpoR\"\n]", "notifications.new-notifications-list-top-nav #qlAyBsU86YvtDyZx573L": "array:5 [\n  \"data\" => array:11 [\n    \"user\" => null\n    \"perPage\" => null\n    \"assignedAsset\" => null\n    \"contractsIds\" => null\n    \"accessBuildingsIds\" => null\n    \"currentDate\" => null\n    \"currentDateTime\" => null\n    \"readyToLoad\" => null\n    \"configOciLink\" => null\n    \"ociLink\" => null\n    \"selectedLanguage\" => null\n  ]\n  \"name\" => \"notifications.new-notifications-list-top-nav\"\n  \"view\" => \"livewire.notifications.new-notifications-list-top-nav\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav\"\n  \"id\" => \"qlAyBsU86YvtDyZx573L\"\n]", "menu.aside-nav-list #": "array:7 [\n  \"data\" => array:10 [\n    \"user\" => App\\Models\\User {#3576\n      #connection: \"mysql\"\n      #table: \"users\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:78 [\n        \"id\" => 6942\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$bZPxzAseefjVGjn7hoLdBeC4kzUkdyER1EHIujieKix7EVfLRJ552\"\n        \"name\" => \"Testing\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => \"791958357\"\n        \"profile_img\" => null\n        \"emp_id\" => null\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => null\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => null\n        \"role_cities\" => null\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => null\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 21\n        \"project_id\" => 191\n        \"project_user_id\" => 6942\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 2\n        \"last_email_attempt_at\" => \"2025-07-28 15:55:58\"\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-02-18 20:59:50\"\n        \"modified_at\" => \"2025-07-28 16:52:27\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"testing23\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNzEwNzQ2LCJleHAiOjE3NTM3MTQzNDYsIm5iZiI6MTc1MzcxMDc0NiwianRpIjoidVowOWN0Y3MweDJzWWxjeSIsInN1YiI6IjE2NyIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.AKkXobtwuH-ds2xN3RiFvffCOgJNvVh764VKxm49PYk\"\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #original: array:78 [\n        \"id\" => 6942\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$bZPxzAseefjVGjn7hoLdBeC4kzUkdyER1EHIujieKix7EVfLRJ552\"\n        \"name\" => \"Testing\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => \"791958357\"\n        \"profile_img\" => null\n        \"emp_id\" => null\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => null\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => null\n        \"role_cities\" => null\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => null\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 21\n        \"project_id\" => 191\n        \"project_user_id\" => 6942\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 2\n        \"last_email_attempt_at\" => \"2025-07-28 15:55:58\"\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-02-18 20:59:50\"\n        \"modified_at\" => \"2025-07-28 16:52:27\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"testing23\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNzEwNzQ2LCJleHAiOjE3NTM3MTQzNDYsIm5iZiI6MTc1MzcxMDc0NiwianRpIjoidVowOWN0Y3MweDJzWWxjeSIsInN1YiI6IjE2NyIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.AKkXobtwuH-ds2xN3RiFvffCOgJNvVh764VKxm49PYk\"\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #changes: []\n      #casts: array:2 [\n        \"email_verified_at\" => \"datetime\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:2 [\n        \"projectDetails\" => App\\Models\\ProjectsDetails {#3891\n          #connection: \"mysql\"\n          #table: \"projects_details\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:23 [\n            \"id\" => 191\n            \"user_id\" => 7034\n            \"project_name\" => \"Testing\"\n            \"project_name_ar\" => \"Testing23\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => \"20250427115740146654.jpg\"\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2025-02-18 17:58:25\"\n            \"updated_at\" => \"2025-07-28 16:52:27\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 0\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 0\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => null\n            \"contract_end_date\" => null\n            \"share_post\" => 1\n            \"deleted_at\" => null\n            \"crm_workspace_slug\" => \"testing23\"\n          ]\n          #original: array:23 [\n            \"id\" => 191\n            \"user_id\" => 7034\n            \"project_name\" => \"Testing\"\n            \"project_name_ar\" => \"Testing23\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => \"20250427115740146654.jpg\"\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2025-02-18 17:58:25\"\n            \"updated_at\" => \"2025-07-28 16:52:27\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 0\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 0\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => null\n            \"contract_end_date\" => null\n            \"share_post\" => 1\n            \"deleted_at\" => null\n            \"crm_workspace_slug\" => \"testing23\"\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:16 [\n            0 => \"user_id\"\n            1 => \"project_name\"\n            2 => \"project_name_ar\"\n            3 => \"project_image\"\n            4 => \"industry_type\"\n            5 => \"created_by\"\n            6 => \"is_deleted\"\n            7 => \"use_erp_module\"\n            8 => \"use_tenant_module\"\n            9 => \"tenant_status\"\n            10 => \"use_beneficiary_module\"\n            11 => \"benificiary_status\"\n            12 => \"community_status\"\n            13 => \"contract_status\"\n            14 => \"share_post\"\n            15 => \"use_crm_module\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n          #excludedAttributes: []\n          +auditEvent: null\n          +auditCustomOld: null\n          +auditCustomNew: null\n          +isCustomEvent: false\n          +preloadedResolverData: []\n        }\n        \"crmUser\" => App\\Models\\CrmUser {#3863\n          #connection: \"mysql\"\n          #table: \"crm_user\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 2\n            \"user_id\" => 6942\n            \"crm_user_id\" => 167\n            \"created_at\" => \"2025-02-18 20:59:50\"\n            \"updated_at\" => \"2025-07-03 16:31:24\"\n            \"instagram_connect\" => 0\n            \"facebook_connect\" => 0\n            \"whatsapp_connect\" => 0\n            \"whatsapp_account_id\" => 164\n            \"whatsapp_number_status\" => \"\"\n          ]\n          #original: array:10 [\n            \"id\" => 2\n            \"user_id\" => 6942\n            \"crm_user_id\" => 167\n            \"created_at\" => \"2025-02-18 20:59:50\"\n            \"updated_at\" => \"2025-07-03 16:31:24\"\n            \"instagram_connect\" => 0\n            \"facebook_connect\" => 0\n            \"whatsapp_connect\" => 0\n            \"whatsapp_account_id\" => 164\n            \"whatsapp_number_status\" => \"\"\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"user_id\"\n            1 => \"crm_user_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n      ]\n      #touches: []\n      +timestamps: true\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: array:62 [\n        0 => \"allow_akaunting\"\n        1 => \"email\"\n        2 => \"password\"\n        3 => \"name\"\n        4 => \"first_name\"\n        5 => \"last_name\"\n        6 => \"apartment\"\n        7 => \"unit_receival_date\"\n        8 => \"later_booking_alert\"\n        9 => \"phone\"\n        10 => \"profile_img\"\n        11 => \"address\"\n        12 => \"country_id\"\n        13 => \"city_id\"\n        14 => \"role_regions\"\n        15 => \"role_cities\"\n        16 => \"asset_categories\"\n        17 => \"properties\"\n        18 => \"contracts\"\n        19 => \"beneficiary\"\n        20 => \"service_provider\"\n        21 => \"user_type\"\n        22 => \"project_id\"\n        23 => \"project_user_id\"\n        24 => \"created_by\"\n        25 => \"status\"\n        26 => \"user_privileges\"\n        27 => \"approved_max_amount\"\n        28 => \"emp_id\"\n        29 => \"profession_id\"\n        30 => \"emp_dept\"\n        31 => \"building_ids\"\n        32 => \"contract_ids\"\n        33 => \"supervisor_id\"\n        34 => \"sp_admin_id\"\n        35 => \"langForSms\"\n        36 => \"deleted_at\"\n        37 => \"otp\"\n        38 => \"temp_password\"\n        39 => \"otp_for_password\"\n        40 => \"otp_for_password_verified\"\n        41 => \"temp_phone_number\"\n        42 => \"favorite_language\"\n        43 => \"is_subcontractors_worker\"\n        44 => \"keeper_warehouses\"\n        45 => \"save_later_date\"\n        46 => \"first_login\"\n        47 => \"is_unit_link\"\n        48 => \"akaunting_vendor_id\"\n        49 => \"akaunting_customer_id\"\n        50 => \"crm_api_token\"\n        51 => \"workspace_slug\"\n        52 => \"is_bma_area_manager\"\n        53 => \"assigned_workers\"\n        54 => \"sleep_mode\"\n        55 => \"offline_mode\"\n        56 => \"attendance_mandatory\"\n        57 => \"admin_level\"\n        58 => \"role\"\n        59 => \"attendance_target\"\n        60 => \"salary\"\n        61 => \"show_extra_info\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #rememberTokenName: \"remember_token\"\n      #accessToken: null\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n    }\n    \"hasAdmin\" => 6942\n    \"projectId\" => null\n    \"project\" => App\\Models\\ProjectsDetails {#3832\n      #connection: \"mysql\"\n      #table: \"projects_details\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:9 [\n        \"id\" => 191\n        \"project_image\" => \"20250427115740146654.jpg\"\n        \"use_beneficiary_module\" => 0\n        \"use_tenant_module\" => 0\n        \"benificiary_status\" => 1\n        \"tenant_status\" => 1\n        \"project_name\" => \"Testing\"\n        \"project_name_ar\" => \"Testing23\"\n        \"use_crm_module\" => 1\n      ]\n      #original: array:9 [\n        \"id\" => 191\n        \"project_image\" => \"20250427115740146654.jpg\"\n        \"use_beneficiary_module\" => 0\n        \"use_tenant_module\" => 0\n        \"benificiary_status\" => 1\n        \"tenant_status\" => 1\n        \"project_name\" => \"Testing\"\n        \"project_name_ar\" => \"Testing23\"\n        \"use_crm_module\" => 1\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:16 [\n        0 => \"user_id\"\n        1 => \"project_name\"\n        2 => \"project_name_ar\"\n        3 => \"project_image\"\n        4 => \"industry_type\"\n        5 => \"created_by\"\n        6 => \"is_deleted\"\n        7 => \"use_erp_module\"\n        8 => \"use_tenant_module\"\n        9 => \"tenant_status\"\n        10 => \"use_beneficiary_module\"\n        11 => \"benificiary_status\"\n        12 => \"community_status\"\n        13 => \"contract_status\"\n        14 => \"share_post\"\n        15 => \"use_crm_module\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n    }\n    \"workOrderMenuItemColor\" => \"#000\"\n    \"flagWorkorderSidebarMenu\" => false\n    \"userPrivileges\" => null\n    \"closedWorkOrderCount\" => null\n    \"maintenanceRequestCount\" => 0\n    \"vendorRegistrationApplicationRequests\" => null\n  ]\n  \"oldData\" => null\n  \"actionQueue\" => null\n  \"name\" => \"menu.aside-nav-list\"\n  \"view\" => \"livewire.menu.aside-nav-list\"\n  \"component\" => \"App\\Http\\Livewire\\Menu\\AsideNavList\"\n  \"id\" => null\n]"}, "count": 3}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"locale": "en", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "g7Dn0dkBBOx6ah5jtIBKp4VpCyNcLrw6Ay9RmcAI", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/user/users-list\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "6942", "plain_user_password": "123456", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/user/users-list", "status_code": "<pre class=sf-dump id=sf-dump-505898065 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-505898065\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1393474422 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1393474422\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-606580168 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-606580168\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-659072661 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://osool-b2g.test/CRMProjects/List?page=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IkovWXRMcmt3cnMvRGxDc2FNdzhZbVE9PSIsInZhbHVlIjoiZTNXTGx1TW80bmthNVgrRkpWYjc0V0JEcEVPalA1VmU5M2R3cTFWTURzV1hKZVhVQ0MxaGUwdjlvODYvd0tNUHRYNEcyODJZN040Wk1lb2FKMlZSbWNPa3NodkRJbzFIMXBadk9IOVV0U3A2MVR4Z3YrcTA2MzhZR2JmUUVxTjgiLCJtYWMiOiI1ZjBhM2RmMTVjMjY5MGI2OGRlNzA4NjdiYmEyNmVhZDVkNTljMjY0ODczZmIyNDE1NTU3Nzk5NDhkZjcwYzA1IiwidGFnIjoiIn0%3D; osool_session=HQecqM64mtjKZmaDw3su1eSxM6Xu57h2IEnYGWqd</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-659072661\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-419806858 data-indent-pad=\"  \"><span class=sf-dump-note>array:39</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://osool-b2g.test/CRMProjects/List?page=1</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IkovWXRMcmt3cnMvRGxDc2FNdzhZbVE9PSIsInZhbHVlIjoiZTNXTGx1TW80bmthNVgrRkpWYjc0V0JEcEVPalA1VmU5M2R3cTFWTURzV1hKZVhVQ0MxaGUwdjlvODYvd0tNUHRYNEcyODJZN040Wk1lb2FKMlZSbWNPa3NodkRJbzFIMXBadk9IOVV0U3A2MVR4Z3YrcTA2MzhZR2JmUUVxTjgiLCJtYWMiOiI1ZjBhM2RmMTVjMjY5MGI2OGRlNzA4NjdiYmEyNmVhZDVkNTljMjY0ODczZmIyNDE1NTU3Nzk5NDhkZjcwYzA1IiwidGFnIjoiIn0%3D; osool_session=HQecqM64mtjKZmaDw3su1eSxM6Xu57h2IEnYGWqd</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">53079</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/user/users-list</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/user/users-list</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.3721</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-419806858\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1862099218 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g7Dn0dkBBOx6ah5jtIBKp4VpCyNcLrw6Ay9RmcAI</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1862099218\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1857801286 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 13:55:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjZjdkxBTzVBdEYyWU1UZjdFaHllc0E9PSIsInZhbHVlIjoibTRUVDhueGdOaGVOU1lkQTZqWDFtQWlwUVJCRld3ZXVKR2VhZ2tGQ2l2RExmMER2RG5zei9waVRpbFhnbEdsdGtFQTdOTXZFQkNlemRsOCtzbGpKaDJTV1hvTGJCb0dYZXlWQVVlbmZZNUZPWG0ySkhSaW41d3cxb01GU1FZYmMiLCJtYWMiOiI1ODkyNWIxNjEzZmYxMGYyMDQyN2IzMThhODU3MzU1YjhjYTYyY2JlYTA4ZGFlNTU5MmVmMDE3YTUzNGViZGE0IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:55:20 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IlBwRzR3OWNaZFBMQnZSQk9ESHdjNGc9PSIsInZhbHVlIjoiV280WWdydGlLbFpWak9maWUxZldMOXNrY0I2OVBoa0xtQmcxa0xOK1VneGx1WjB5TjdNblFsVkgzQitpZW5aRTE2VXVjSE9PaEtsWUlUUFd0VVA4eDRLVzRKMEVKV3NiaHBTYlF2OVl5Tjg1MXZKK01ZYTlScS9IUThiRmRiNnoiLCJtYWMiOiI5ZjEyYjU3ODIwZmM3Y2MxZjAzNDYzZTVlMGY1Nzk3NGFiZTIyZTMxZjAyMWEwY2UzNDA1ODRiNjZlYTlkZWEzIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:55:20 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjZjdkxBTzVBdEYyWU1UZjdFaHllc0E9PSIsInZhbHVlIjoibTRUVDhueGdOaGVOU1lkQTZqWDFtQWlwUVJCRld3ZXVKR2VhZ2tGQ2l2RExmMER2RG5zei9waVRpbFhnbEdsdGtFQTdOTXZFQkNlemRsOCtzbGpKaDJTV1hvTGJCb0dYZXlWQVVlbmZZNUZPWG0ySkhSaW41d3cxb01GU1FZYmMiLCJtYWMiOiI1ODkyNWIxNjEzZmYxMGYyMDQyN2IzMThhODU3MzU1YjhjYTYyY2JlYTA4ZGFlNTU5MmVmMDE3YTUzNGViZGE0IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:55:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IlBwRzR3OWNaZFBMQnZSQk9ESHdjNGc9PSIsInZhbHVlIjoiV280WWdydGlLbFpWak9maWUxZldMOXNrY0I2OVBoa0xtQmcxa0xOK1VneGx1WjB5TjdNblFsVkgzQitpZW5aRTE2VXVjSE9PaEtsWUlUUFd0VVA4eDRLVzRKMEVKV3NiaHBTYlF2OVl5Tjg1MXZKK01ZYTlScS9IUThiRmRiNnoiLCJtYWMiOiI5ZjEyYjU3ODIwZmM3Y2MxZjAzNDYzZTVlMGY1Nzk3NGFiZTIyZTMxZjAyMWEwY2UzNDA1ODRiNjZlYTlkZWEzIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:55:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1857801286\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-431628706 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g7Dn0dkBBOx6ah5jtIBKp4VpCyNcLrw6Ay9RmcAI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://osool-b2g.test/user/users-list</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>6942</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-431628706\", {\"maxDepth\":0})</script>\n"}}