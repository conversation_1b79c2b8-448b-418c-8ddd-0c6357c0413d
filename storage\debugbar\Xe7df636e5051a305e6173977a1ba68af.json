{"__meta": {"id": "Xe7df636e5051a305e6173977a1ba68af", "datetime": "2025-07-28 16:44:49", "utime": 1753710289.011512, "method": "GET", "uri": "/workspace/adminProjectsListAjax?search_text=&_=1753710288090", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 10, "messages": [{"message": "[16:44:48] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753710288.847589, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:48] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2418", "message_html": null, "is_string": false, "label": "warning", "time": 1753710288.901063, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:48] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2478", "message_html": null, "is_string": false, "label": "warning", "time": 1753710288.901153, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:48] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3619", "message_html": null, "is_string": false, "label": "warning", "time": 1753710288.901665, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:48] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710288.931435, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:48] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710288.94695, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:48] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710288.959127, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:48] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710288.973309, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:48] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710288.987307, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:49] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710289.001687, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753710288.361504, "end": 1753710289.011541, "duration": 0.6500368118286133, "duration_str": "650ms", "measures": [{"label": "Booting", "start": 1753710288.361504, "relative_start": 0, "end": 1753710288.822717, "relative_end": 1753710288.822717, "duration": 0.4612128734588623, "duration_str": "461ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753710288.822734, "relative_start": 0.4612300395965576, "end": 1753710289.011543, "relative_end": 2.1457672119140625e-06, "duration": 0.18880891799926758, "duration_str": "189ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 37937992, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET workspace/adminProjectsListAjax/{id?}", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController@adminProjectListAjax", "as": "workspace.list_projects.ajax", "namespace": "App\\Http\\Controllers\\Admin\\Workspace", "prefix": "/workspace", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php&line=457\">\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:457-528</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.08201, "accumulated_duration_str": "82.01ms", "statements": [{"sql": "select * from `users` where `id` = 21 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00332, "duration_str": "3.32ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 4.048}, {"sql": "select count(*) as aggregate from (select `projects_details`.*, `user_projects`.`status` from `projects_details` left join `user_projects` on `user_projects`.`project_id` = `projects_details`.`id` where (`projects_details`.`project_name` LIKE '%%' and `projects_details`.`deleted_at` is null and `projects_details`.`is_deleted` = '0') and `projects_details`.`user_id` = 21 and `projects_details`.`deleted_at` is null group by `projects_details`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["%%", "0", "21"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 490}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00214, "duration_str": "2.14ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:490", "connection": "osool_test_db", "start_percent": 4.048, "width_percent": 2.609}, {"sql": "select count(*) as aggregate from `projects_details` left join `users` on `users`.`id` = `projects_details`.`user_id` where ((`projects_details`.`project_name` LIKE '%%' and `projects_details`.`deleted_at` is null and `projects_details`.`is_deleted` = 0) or (`projects_details`.`project_name_ar` LIKE '%%' and `projects_details`.`deleted_at` is null and `projects_details`.`is_deleted` = 0) and `projects_details`.`deleted_at` is null) and `projects_details`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["%%", "0", "%%", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 499}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:499", "connection": "osool_test_db", "start_percent": 6.658, "width_percent": 1.11}, {"sql": "select `projects_details`.*, `users`.`name` from `projects_details` left join `users` on `users`.`id` = `projects_details`.`user_id` where ((`projects_details`.`project_name` LIKE '%%' and `projects_details`.`deleted_at` is null and `projects_details`.`is_deleted` = 0) or (`projects_details`.`project_name_ar` LIKE '%%' and `projects_details`.`deleted_at` is null and `projects_details`.`is_deleted` = 0) and `projects_details`.`deleted_at` is null) and `projects_details`.`deleted_at` is null order by `id` desc limit 6 offset 0", "type": "query", "params": [], "bindings": ["%%", "0", "%%", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 499}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:499", "connection": "osool_test_db", "start_percent": 7.767, "width_percent": 0.817}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 205 limit 1", "type": "query", "params": [], "bindings": ["admin", "205"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.013529999999999999, "duration_str": "13.53ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 8.584, "width_percent": 16.498}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 204 limit 1", "type": "query", "params": [], "bindings": ["admin", "204"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01391, "duration_str": "13.91ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 25.082, "width_percent": 16.961}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 203 limit 1", "type": "query", "params": [], "bindings": ["admin", "203"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01071, "duration_str": "10.71ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 42.044, "width_percent": 13.059}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 202 limit 1", "type": "query", "params": [], "bindings": ["admin", "202"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01265, "duration_str": "12.65ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 55.103, "width_percent": 15.425}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 201 limit 1", "type": "query", "params": [], "bindings": ["admin", "201"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0119, "duration_str": "11.9ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 70.528, "width_percent": 14.51}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 200 limit 1", "type": "query", "params": [], "bindings": ["admin", "200"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01227, "duration_str": "12.27ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 85.038, "width_percent": 14.962}]}, "models": {"data": {"App\\Models\\ProjectsDetails": 6, "App\\Models\\User": 1}, "count": 7}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"locale": "en", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "MoHVz4Tx7nVo7GO6J30zRNXQMrc7mCwjsdZxDGeF", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/workspace/projects\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "21", "plain_user_password": "123456", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/workspace/adminProjectsListAjax", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1659687419 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search_text</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1753710288090</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1659687419\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1573642015 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search_text</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1753710288090</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1573642015\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1240790334 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/workspace/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IkovNUFuR3pXdDZZU3NSVHZkZW90QUE9PSIsInZhbHVlIjoiVnJGV3BBWHNtcDRmRlVlNUZBTGIyRTdjdW11YlBZZC9FaVF0V2NRdlB5bTJvVVF0VE5NS2FzS0JQNlJ6YnBqaVI1eWkyWWs2R3NRWG40ZDRWZmFCMXFOZmQzRUx5SHRpb1VGQjUxdkltbkF1cEdSdXlVcmFrazZrWFFsZGJIYjMiLCJtYWMiOiJiZDg4NWE3NDVjZmUzZGI2NWM2OTI1MGY5NTNhNjkyZmFlNWRiZGM2M2EwMTNhMDMyMDExMDhhZTk2MjRlYTk5IiwidGFnIjoiIn0%3D; osool_session=8toBA6Hyc6x7GpjwRexqOcFlfcHTDoAYiqeojwtM</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1240790334\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1213078505 data-indent-pad=\"  \"><span class=sf-dump-note>array:40</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/workspace/projects</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IkovNUFuR3pXdDZZU3NSVHZkZW90QUE9PSIsInZhbHVlIjoiVnJGV3BBWHNtcDRmRlVlNUZBTGIyRTdjdW11YlBZZC9FaVF0V2NRdlB5bTJvVVF0VE5NS2FzS0JQNlJ6YnBqaVI1eWkyWWs2R3NRWG40ZDRWZmFCMXFOZmQzRUx5SHRpb1VGQjUxdkltbkF1cEdSdXlVcmFrazZrWFFsZGJIYjMiLCJtYWMiOiJiZDg4NWE3NDVjZmUzZGI2NWM2OTI1MGY5NTNhNjkyZmFlNWRiZGM2M2EwMTNhMDMyMDExMDhhZTk2MjRlYTk5IiwidGFnIjoiIn0%3D; osool_session=8toBA6Hyc6x7GpjwRexqOcFlfcHTDoAYiqeojwtM</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51840</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"32 characters\">/workspace/adminProjectsListAjax</span>\"\n  \"<span class=sf-dump-key>REDIRECT_QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"28 characters\">search_text=&amp;_=1753710288090</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"28 characters\">search_text=&amp;_=1753710288090</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"61 characters\">/workspace/adminProjectsListAjax?search_text=&amp;_=1753710288090</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753710288.3615</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753710288</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1213078505\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-741096528 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MoHVz4Tx7nVo7GO6J30zRNXQMrc7mCwjsdZxDGeF</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-741096528\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-774116002 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 13:44:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik5tQWhUNHdsVjZvNDJRZG9BQU5BVUE9PSIsInZhbHVlIjoiTjd2TGtCQ1orQXp0cTJtU2R1L1E5V0k1b2pGd3phOGlYTkpiNXdZeDMrdW1pR3d4Q054d2k4ckU0ejRtM0ltb0pHMmxHMW9jckp2WDllUUNRUVRLTlh1QlNveG9LWmxBOXdJMjdqaEdwVnBvU0UwdWFGaWJOaVVQMlVySVZmbDUiLCJtYWMiOiJjOGQ2OTU0Njc3NTExODg4MGI4YzdkNzA2ZDE4MjVlMTBmZGE2NTFkOTgxZmQzNDBkMzY0ODQ0OTkzMzY5ZTM3IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:44:49 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IjVaa01ESE9aaWQ0elkvakEwUXRNdWc9PSIsInZhbHVlIjoiTHV1d3hNSFRNUGlJbnJlcVYrVjlnWW15QktXa3hWU3Y5T2NzenJidDVXbWhLdzFsWVFyYnpodmdENWJOMmxEYTgwVEttSTVhb1JjWFIrQ2htUXFFUVJxc1ZWZUl4TGV2OU0rK0huS0tPSnRRYUJQYldaOXREYnZlY0FhRDVaVUMiLCJtYWMiOiJiYjEyZDE4ZTBjMjliMTY0N2Q2MjQ4MTc3ZjRiNTk2OTdkOWQ4NDk2ZTljZTA0MGU4M2FlMTAzNjQ1ZGI5YjBkIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:44:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik5tQWhUNHdsVjZvNDJRZG9BQU5BVUE9PSIsInZhbHVlIjoiTjd2TGtCQ1orQXp0cTJtU2R1L1E5V0k1b2pGd3phOGlYTkpiNXdZeDMrdW1pR3d4Q054d2k4ckU0ejRtM0ltb0pHMmxHMW9jckp2WDllUUNRUVRLTlh1QlNveG9LWmxBOXdJMjdqaEdwVnBvU0UwdWFGaWJOaVVQMlVySVZmbDUiLCJtYWMiOiJjOGQ2OTU0Njc3NTExODg4MGI4YzdkNzA2ZDE4MjVlMTBmZGE2NTFkOTgxZmQzNDBkMzY0ODQ0OTkzMzY5ZTM3IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:44:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IjVaa01ESE9aaWQ0elkvakEwUXRNdWc9PSIsInZhbHVlIjoiTHV1d3hNSFRNUGlJbnJlcVYrVjlnWW15QktXa3hWU3Y5T2NzenJidDVXbWhLdzFsWVFyYnpodmdENWJOMmxEYTgwVEttSTVhb1JjWFIrQ2htUXFFUVJxc1ZWZUl4TGV2OU0rK0huS0tPSnRRYUJQYldaOXREYnZlY0FhRDVaVUMiLCJtYWMiOiJiYjEyZDE4ZTBjMjliMTY0N2Q2MjQ4MTc3ZjRiNTk2OTdkOWQ4NDk2ZTljZTA0MGU4M2FlMTAzNjQ1ZGI5YjBkIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:44:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-774116002\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1432949932 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MoHVz4Tx7nVo7GO6J30zRNXQMrc7mCwjsdZxDGeF</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/workspace/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>21</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1432949932\", {\"maxDepth\":0})</script>\n"}}