{"__meta": {"id": "X1c17addb832901a6df574c3ca74aef3a", "datetime": "2025-07-28 16:55:22", "utime": **********.4453, "method": "GET", "uri": "/user/userListAjax?search_text=&page_length=10&_=1753710921146", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 16, "messages": [{"message": "[16:55:22] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.292515, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:22] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.333394, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:22] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.333438, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:22] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.333477, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:22] LOG.warning: Optional parameter $serviceProviderId declared before required parameter $search is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\UserTrait.php on line 328", "message_html": null, "is_string": false, "label": "warning", "time": **********.336372, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:22] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2418", "message_html": null, "is_string": false, "label": "warning", "time": **********.392617, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:22] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2478", "message_html": null, "is_string": false, "label": "warning", "time": **********.392706, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:22] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3619", "message_html": null, "is_string": false, "label": "warning", "time": **********.393363, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:22] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": **********.436446, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:22] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": **********.436598, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:22] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": **********.436685, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:22] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": **********.436764, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:22] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": **********.436846, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:22] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": **********.436921, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:22] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": **********.437021, "xdebug_link": null, "collector": "log"}, {"message": "[16:55:22] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": **********.437213, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753710921.301407, "end": **********.445329, "duration": 1.1439218521118164, "duration_str": "1.14s", "measures": [{"label": "Booting", "start": 1753710921.301407, "relative_start": 0, "end": **********.264475, "relative_end": **********.264475, "duration": 0.9630680084228516, "duration_str": "963ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.264486, "relative_start": 0.9630789756774902, "end": **********.445331, "relative_end": 2.1457672119140625e-06, "duration": 0.18084502220153809, "duration_str": "181ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 39455720, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET user/userListAjax/{id?}", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Admin\\User\\UserControllerNew@userListAjax", "as": "users.list.ajax", "namespace": "App\\Http\\Controllers\\Admin\\User", "prefix": "/user", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php&line=264\">\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php:264-475</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03855, "accumulated_duration_str": "38.55ms", "statements": [{"sql": "select * from `users` where `id` = 6942 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6942"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0035, "duration_str": "3.5ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 9.079}, {"sql": "select count(*) as aggregate from (select `service_providers`.`global_sp`, `users`.*, `cities`.`name_en` as `city_name_en`, `cities`.`name_ar` as `city_name_ar`, `worker_professions`.`profession_en`, `worker_professions`.`profession_ar` from `users` left join `worker_professions` on `worker_professions`.`id` = `users`.`profession_id` left join `service_providers` on `service_providers`.`id` = `users`.`service_provider` left join `cities` on `cities`.`id` = `users`.`city_id` where `users`.`id` != 6942 and ( `service_providers`.`global_sp` = '0' or `service_providers`.`global_sp` IS NULL )  and `user_type` != 'osool_admin' and `user_type` != 'tenant' and `user_type` != 'admin' and `user_type` != 'super_admin' and `project_user_id` = 6942 and (`users`.`deleted_at` is null and `users`.`is_deleted` = 'no') and `users`.`deleted_at` is null group by `users`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["6942", "osool_admin", "tenant", "admin", "super_admin", "6942", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php", "line": 459}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.02036, "duration_str": "20.36ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php:459", "connection": "osool_test_db", "start_percent": 9.079, "width_percent": 52.815}, {"sql": "select `service_providers`.`global_sp`, `users`.*, `cities`.`name_en` as `city_name_en`, `cities`.`name_ar` as `city_name_ar`, `worker_professions`.`profession_en`, `worker_professions`.`profession_ar` from `users` left join `worker_professions` on `worker_professions`.`id` = `users`.`profession_id` left join `service_providers` on `service_providers`.`id` = `users`.`service_provider` left join `cities` on `cities`.`id` = `users`.`city_id` where `users`.`id` != 6942 and ( `service_providers`.`global_sp` = '0' or `service_providers`.`global_sp` IS NULL )  and `user_type` != 'osool_admin' and `user_type` != 'tenant' and `user_type` != 'admin' and `user_type` != 'super_admin' and `project_user_id` = 6942 and (`users`.`deleted_at` is null and `users`.`is_deleted` = 'no') and `users`.`deleted_at` is null group by `users`.`id` order by `users`.`id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["6942", "osool_admin", "tenant", "admin", "super_admin", "6942", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php", "line": 459}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.01469, "duration_str": "14.69ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php:459", "connection": "osool_test_db", "start_percent": 61.894, "width_percent": 38.106}]}, "models": {"data": {"App\\Models\\User": 9}, "count": 9}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"locale": "en", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "g7Dn0dkBBOx6ah5jtIBKp4VpCyNcLrw6Ay9RmcAI", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/user/users-list\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "6942", "plain_user_password": "123456", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/user/userListAjax", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1991879504 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search_text</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>page_length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1753710921146</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1991879504\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-459760528 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search_text</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>page_length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1753710921146</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-459760528\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-573084017 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://osool-b2g.test/user/users-list</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IjZjdkxBTzVBdEYyWU1UZjdFaHllc0E9PSIsInZhbHVlIjoibTRUVDhueGdOaGVOU1lkQTZqWDFtQWlwUVJCRld3ZXVKR2VhZ2tGQ2l2RExmMER2RG5zei9waVRpbFhnbEdsdGtFQTdOTXZFQkNlemRsOCtzbGpKaDJTV1hvTGJCb0dYZXlWQVVlbmZZNUZPWG0ySkhSaW41d3cxb01GU1FZYmMiLCJtYWMiOiI1ODkyNWIxNjEzZmYxMGYyMDQyN2IzMThhODU3MzU1YjhjYTYyY2JlYTA4ZGFlNTU5MmVmMDE3YTUzNGViZGE0IiwidGFnIjoiIn0%3D; osool_session=2LApaI4hCuxuH6ZT2fYTXpLbvQfplo3GWgEaKH4B</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-573084017\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-455815691 data-indent-pad=\"  \"><span class=sf-dump-note>array:40</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://osool-b2g.test/user/users-list</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IjZjdkxBTzVBdEYyWU1UZjdFaHllc0E9PSIsInZhbHVlIjoibTRUVDhueGdOaGVOU1lkQTZqWDFtQWlwUVJCRld3ZXVKR2VhZ2tGQ2l2RExmMER2RG5zei9waVRpbFhnbEdsdGtFQTdOTXZFQkNlemRsOCtzbGpKaDJTV1hvTGJCb0dYZXlWQVVlbmZZNUZPWG0ySkhSaW41d3cxb01GU1FZYmMiLCJtYWMiOiI1ODkyNWIxNjEzZmYxMGYyMDQyN2IzMThhODU3MzU1YjhjYTYyY2JlYTA4ZGFlNTU5MmVmMDE3YTUzNGViZGE0IiwidGFnIjoiIn0%3D; osool_session=2LApaI4hCuxuH6ZT2fYTXpLbvQfplo3GWgEaKH4B</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">53091</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"18 characters\">/user/userListAjax</span>\"\n  \"<span class=sf-dump-key>REDIRECT_QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"43 characters\">search_text=&amp;page_length=10&amp;_=1753710921146</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"43 characters\">search_text=&amp;page_length=10&amp;_=1753710921146</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"62 characters\">/user/userListAjax?search_text=&amp;page_length=10&amp;_=1753710921146</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753710921.3014</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753710921</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-455815691\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-75185230 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g7Dn0dkBBOx6ah5jtIBKp4VpCyNcLrw6Ay9RmcAI</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-75185230\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1832394220 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 13:55:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IldHSUpMT0ZtVlloTmFBMEloaUI0TGc9PSIsInZhbHVlIjoiRERsOXVwdHJVczNIT1FqVjNEMktzY0VBcE94Sy9pcEhib2lRenhzWS9FS1R2WWtGb0w2Rkgxa1dwNVJUb293TXFMdnBQRkNDOVhjSndYcnVkbjdHQ0poYmgyNEUrbFhtOXB1aUErTzV0WVFxTXpTRDcwSW1OemtzT3ovS2dwanAiLCJtYWMiOiJmMTkwM2RiNWI4NDljZWVhZWM4YTJhYjRlMDRlYzM0N2QyOGJjNzc0Y2NlNjZkMDYyMjRjNmUxNmE2NWYwNTc4IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:55:22 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IllTUzBsaW5OU0pIMjAwV3lUSktxUlE9PSIsInZhbHVlIjoiVjN5aXQvU243QzJvVDA5eXd4WmprdFl6d0d3MlBQb2pFYzlFT3Vqa3pZdWVSd0tnSGk1d0FhZnFMOENBUDROblBFWHVhNStZUDVCenFmM3FEd0lRaUl3QlZsMTBvbFNMK294M3A1SEFoam1ud1VIckoxbDVXaXB2SmNZUDlJSDAiLCJtYWMiOiIxZmJkMGZjYjUwM2QyM2IxM2E4YTU5YTRiNzc0ODJjOWQ4MjBlZTA3ODNmYzcwZTgyY2Q0MGFmMzA4MGE2MGY5IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:55:22 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IldHSUpMT0ZtVlloTmFBMEloaUI0TGc9PSIsInZhbHVlIjoiRERsOXVwdHJVczNIT1FqVjNEMktzY0VBcE94Sy9pcEhib2lRenhzWS9FS1R2WWtGb0w2Rkgxa1dwNVJUb293TXFMdnBQRkNDOVhjSndYcnVkbjdHQ0poYmgyNEUrbFhtOXB1aUErTzV0WVFxTXpTRDcwSW1OemtzT3ovS2dwanAiLCJtYWMiOiJmMTkwM2RiNWI4NDljZWVhZWM4YTJhYjRlMDRlYzM0N2QyOGJjNzc0Y2NlNjZkMDYyMjRjNmUxNmE2NWYwNTc4IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:55:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IllTUzBsaW5OU0pIMjAwV3lUSktxUlE9PSIsInZhbHVlIjoiVjN5aXQvU243QzJvVDA5eXd4WmprdFl6d0d3MlBQb2pFYzlFT3Vqa3pZdWVSd0tnSGk1d0FhZnFMOENBUDROblBFWHVhNStZUDVCenFmM3FEd0lRaUl3QlZsMTBvbFNMK294M3A1SEFoam1ud1VIckoxbDVXaXB2SmNZUDlJSDAiLCJtYWMiOiIxZmJkMGZjYjUwM2QyM2IxM2E4YTU5YTRiNzc0ODJjOWQ4MjBlZTA3ODNmYzcwZTgyY2Q0MGFmMzA4MGE2MGY5IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:55:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1832394220\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-361957410 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g7Dn0dkBBOx6ah5jtIBKp4VpCyNcLrw6Ay9RmcAI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://osool-b2g.test/user/users-list</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>6942</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-361957410\", {\"maxDepth\":0})</script>\n"}}