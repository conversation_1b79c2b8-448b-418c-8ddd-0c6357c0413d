{"__meta": {"id": "X5e26f10b3fd42f617b354b46144ca3de", "datetime": "2025-07-28 16:44:57", "utime": 1753710297.620144, "method": "GET", "uri": "/workspace/adminProjectsListAjax?search_text=testin&_=1753710288095", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 9, "messages": [{"message": "[16:44:55] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753710295.075147, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:55] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2418", "message_html": null, "is_string": false, "label": "warning", "time": 1753710295.164676, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:55] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2478", "message_html": null, "is_string": false, "label": "warning", "time": 1753710295.164781, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:55] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3619", "message_html": null, "is_string": false, "label": "warning", "time": 1753710295.165878, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:55] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710295.203152, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:55] LOG.debug: Checking existence of file: uploads/project_images/20250427115740146654.jpg", "message_html": null, "is_string": false, "label": "debug", "time": 1753710295.224156, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:57] LOG.debug: File exists: true", "message_html": null, "is_string": false, "label": "debug", "time": 1753710297.565737, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:57] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710297.587482, "xdebug_link": null, "collector": "log"}, {"message": "[16:44:57] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710297.608516, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753710294.271526, "end": 1753710297.620179, "duration": 3.3486528396606445, "duration_str": "3.35s", "measures": [{"label": "Booting", "start": 1753710294.271526, "relative_start": 0, "end": 1753710295.039954, "relative_end": 1753710295.039954, "duration": 0.768427848815918, "duration_str": "768ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753710295.039968, "relative_start": 0.768441915512085, "end": 1753710297.62018, "relative_end": 9.5367431640625e-07, "duration": 2.580211877822876, "duration_str": "2.58s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 52084296, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET workspace/adminProjectsListAjax/{id?}", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController@adminProjectListAjax", "as": "workspace.list_projects.ajax", "namespace": "App\\Http\\Controllers\\Admin\\Workspace", "prefix": "/workspace", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php&line=457\">\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:457-528</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.07998, "accumulated_duration_str": "79.98ms", "statements": [{"sql": "select * from `users` where `id` = 21 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0034300000000000003, "duration_str": "3.43ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 4.289}, {"sql": "select count(*) as aggregate from (select `projects_details`.*, `user_projects`.`status` from `projects_details` left join `user_projects` on `user_projects`.`project_id` = `projects_details`.`id` where (`projects_details`.`project_name` LIKE '%testin%' and `projects_details`.`deleted_at` is null and `projects_details`.`is_deleted` = '0') and `projects_details`.`user_id` = 21 and `projects_details`.`deleted_at` is null group by `projects_details`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["%testin%", "0", "21"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 490}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0019, "duration_str": "1.9ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:490", "connection": "osool_test_db", "start_percent": 4.289, "width_percent": 2.376}, {"sql": "select count(*) as aggregate from `projects_details` left join `users` on `users`.`id` = `projects_details`.`user_id` where ((`projects_details`.`project_name` LIKE '%testin%' and `projects_details`.`deleted_at` is null and `projects_details`.`is_deleted` = 0) or (`projects_details`.`project_name_ar` LIKE '%testin%' and `projects_details`.`deleted_at` is null and `projects_details`.`is_deleted` = 0) and `projects_details`.`deleted_at` is null) and `projects_details`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["%testin%", "0", "%testin%", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 499}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:499", "connection": "osool_test_db", "start_percent": 6.664, "width_percent": 1.238}, {"sql": "select `projects_details`.*, `users`.`name` from `projects_details` left join `users` on `users`.`id` = `projects_details`.`user_id` where ((`projects_details`.`project_name` LIKE '%testin%' and `projects_details`.`deleted_at` is null and `projects_details`.`is_deleted` = 0) or (`projects_details`.`project_name_ar` LIKE '%testin%' and `projects_details`.`deleted_at` is null and `projects_details`.`is_deleted` = 0) and `projects_details`.`deleted_at` is null) and `projects_details`.`deleted_at` is null order by `id` desc limit 6 offset 0", "type": "query", "params": [], "bindings": ["%testin%", "0", "%testin%", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 499}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0011, "duration_str": "1.1ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:499", "connection": "osool_test_db", "start_percent": 7.902, "width_percent": 1.375}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 193 limit 1", "type": "query", "params": [], "bindings": ["admin", "193"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.016190000000000003, "duration_str": "16.19ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 9.277, "width_percent": 20.243}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 191 limit 1", "type": "query", "params": [], "bindings": ["admin", "191"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.018699999999999998, "duration_str": "18.7ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 29.52, "width_percent": 23.381}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 181 limit 1", "type": "query", "params": [], "bindings": ["admin", "181"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01823, "duration_str": "18.23ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 52.901, "width_percent": 22.793}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 142 limit 1", "type": "query", "params": [], "bindings": ["admin", "142"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.019440000000000002, "duration_str": "19.44ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 75.694, "width_percent": 24.306}]}, "models": {"data": {"App\\Models\\ProjectsDetails": 4, "App\\Models\\User": 1}, "count": 5}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"locale": "en", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "MoHVz4Tx7nVo7GO6J30zRNXQMrc7mCwjsdZxDGeF", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/_debugbar/open?id=Xe653adfd7fa96226daec6b6781867bee&op=get\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "21", "plain_user_password": "123456", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/workspace/adminProjectsListAjax", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-292976488 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search_text</span>\" => \"<span class=sf-dump-str title=\"6 characters\">testin</span>\"\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1753710288095</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-292976488\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-988300839 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search_text</span>\" => \"<span class=sf-dump-str title=\"6 characters\">testin</span>\"\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1753710288095</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-988300839\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2346522 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/workspace/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6ImU0cnBNRFlBOWdJVW00c2d3RHluM0E9PSIsInZhbHVlIjoibXJnMDF0S3ZuQ2x1QTJLQ2kzb0tIQXBhRi9TVGV2a1VZNE5tOWNPaGRqdGZwS0FqZlE1SGJPd2kxb2laTEN2WlU1YVZyckRSUlE5blVSYnFpenZIWnZld1o5VU1KZ1p1cGpTOG43bUNEN01qVlNOTEFTLzZMMjVGMlcvd1pBQXciLCJtYWMiOiI0ZjFlNGE4MzI0NjUzZjhiZGU4YmIzNWIzMzU0NTkwNzQzYjE4NzNjMTU2OTJkMTEyMzQ4NTM3MmQ4OTVmMjE5IiwidGFnIjoiIn0%3D; osool_session=f1oR89ybkQRN4yorX1bIHjhV99BPIggTu71kAkDC</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2346522\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-2126291353 data-indent-pad=\"  \"><span class=sf-dump-note>array:40</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/workspace/projects</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6ImU0cnBNRFlBOWdJVW00c2d3RHluM0E9PSIsInZhbHVlIjoibXJnMDF0S3ZuQ2x1QTJLQ2kzb0tIQXBhRi9TVGV2a1VZNE5tOWNPaGRqdGZwS0FqZlE1SGJPd2kxb2laTEN2WlU1YVZyckRSUlE5blVSYnFpenZIWnZld1o5VU1KZ1p1cGpTOG43bUNEN01qVlNOTEFTLzZMMjVGMlcvd1pBQXciLCJtYWMiOiI0ZjFlNGE4MzI0NjUzZjhiZGU4YmIzNWIzMzU0NTkwNzQzYjE4NzNjMTU2OTJkMTEyMzQ4NTM3MmQ4OTVmMjE5IiwidGFnIjoiIn0%3D; osool_session=f1oR89ybkQRN4yorX1bIHjhV99BPIggTu71kAkDC</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51858</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"32 characters\">/workspace/adminProjectsListAjax</span>\"\n  \"<span class=sf-dump-key>REDIRECT_QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"34 characters\">search_text=testin&amp;_=1753710288095</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"34 characters\">search_text=testin&amp;_=1753710288095</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"67 characters\">/workspace/adminProjectsListAjax?search_text=testin&amp;_=1753710288095</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753710294.2715</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753710294</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2126291353\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1975772791 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MoHVz4Tx7nVo7GO6J30zRNXQMrc7mCwjsdZxDGeF</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1975772791\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1297121654 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 13:44:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ill2NHdpdEx3bnA0aXI3SVZIRTZoZWc9PSIsInZhbHVlIjoidkpkb05Md3FHRTdYSndNUWluZ1ptT213NjNTbnJRaGw1T3pVNlptMkZUb2RCYktKZEFybXR1ZWt3UXNlbFd4RlczRDJXY0pNUE13T3MzOGQvWUlRdExBTExIZGV1VmhJQlkwQUlxY3pyWVlmSDFZQURCM2ZONitKb1doaWRnM00iLCJtYWMiOiI3NmRhMDI1Y2EzYzk1NDZkMjlmMDgwYjlhZGEwNWNlZTI3MjdmNGI0MDYxN2ZiNjRmMDYwOGY2NjYxYTZhYzYxIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:44:57 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IjZHaEJLdmozZTFkamtzajhiS0pmWlE9PSIsInZhbHVlIjoiaGoyQ1ZjbEY3a1dzV2lvSzh6S0ZhTWI4R0tQTmc5eVNwdnEvMFlCcTRvaUkxRHNyNkE2QlZhNzVSWUlyRTVEUUlSWVZIa2R1Zjc2T01oVVJwSndud3c0VURJSE9haytPeUQrSXVuWGRLZkI1aWRGK2hGdTd5ZzRhd3hpKzZxc0wiLCJtYWMiOiIwZTE5ODFiYjAyNzQ4YTNkMjZhMzQ0OTQ5M2EzZTc4ZTlmNDFlNzQyM2JkN2JhODE3MjkxNjMyMjQwZWM2MmQ0IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:44:57 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ill2NHdpdEx3bnA0aXI3SVZIRTZoZWc9PSIsInZhbHVlIjoidkpkb05Md3FHRTdYSndNUWluZ1ptT213NjNTbnJRaGw1T3pVNlptMkZUb2RCYktKZEFybXR1ZWt3UXNlbFd4RlczRDJXY0pNUE13T3MzOGQvWUlRdExBTExIZGV1VmhJQlkwQUlxY3pyWVlmSDFZQURCM2ZONitKb1doaWRnM00iLCJtYWMiOiI3NmRhMDI1Y2EzYzk1NDZkMjlmMDgwYjlhZGEwNWNlZTI3MjdmNGI0MDYxN2ZiNjRmMDYwOGY2NjYxYTZhYzYxIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:44:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IjZHaEJLdmozZTFkamtzajhiS0pmWlE9PSIsInZhbHVlIjoiaGoyQ1ZjbEY3a1dzV2lvSzh6S0ZhTWI4R0tQTmc5eVNwdnEvMFlCcTRvaUkxRHNyNkE2QlZhNzVSWUlyRTVEUUlSWVZIa2R1Zjc2T01oVVJwSndud3c0VURJSE9haytPeUQrSXVuWGRLZkI1aWRGK2hGdTd5ZzRhd3hpKzZxc0wiLCJtYWMiOiIwZTE5ODFiYjAyNzQ4YTNkMjZhMzQ0OTQ5M2EzZTc4ZTlmNDFlNzQyM2JkN2JhODE3MjkxNjMyMjQwZWM2MmQ0IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:44:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1297121654\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-811957739 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MoHVz4Tx7nVo7GO6J30zRNXQMrc7mCwjsdZxDGeF</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"80 characters\">http://osool-b2g.test/_debugbar/open?id=Xe653adfd7fa96226daec6b6781867bee&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>21</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-811957739\", {\"maxDepth\":0})</script>\n"}}