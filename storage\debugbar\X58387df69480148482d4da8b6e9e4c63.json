{"__meta": {"id": "X58387df69480148482d4da8b6e9e4c63", "datetime": "2025-07-28 16:44:19", "utime": **********.4023, "method": "GET", "uri": "/check-crm-workspace", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[16:44:19] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.339327, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753710258.85067, "end": **********.402324, "duration": 0.5516538619995117, "duration_str": "552ms", "measures": [{"label": "Booting", "start": 1753710258.85067, "relative_start": 0, "end": **********.308831, "relative_end": **********.308831, "duration": 0.4581608772277832, "duration_str": "458ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.308859, "relative_start": 0.****************, "end": **********.402326, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "93.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "31MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET check-crm-workspace", "middleware": "web, auth", "uses": "Closure() {#1964\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#754 …}\n  file: \"C:\\laragon\\www\\Osool-B2G\\routes\\web.php\"\n  line: \"288 to 290\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\routes\\web.php&line=288\">\\routes\\web.php:288-290</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00364, "accumulated_duration_str": "3.64ms", "statements": [{"sql": "select * from `users` where `id` = 6942 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6942"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00364, "duration_str": "3.64ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": 1}, "count": 1}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"locale": "en", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "4C9vQdJyFZ94hAMjhPJDc0YkqTwfCybUFigJMtCN", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/_debugbar/open?id=Xdd88a3352a372ba0ef80f6e0d6680dac&op=get\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "6942", "plain_user_password": "123456", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/check-crm-workspace", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1380927423 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1380927423\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1960276848 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1960276848\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-123280802 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://osool-b2g.test/dashboards/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IjlHUS9tdTNLRGRyLzA5TG1pZmJuM2c9PSIsInZhbHVlIjoibUFmNFFXWnZwOW1Gb1Era0xxdVB4Qjl4YUt1bnJGV0ZaZ1E4dk9zb2s2SzJOUEFwUklhRUNyNXMzOHgvbkVvMmErL3kxNElKNVNWaGJtT2doeEd4UUZ3V3VKWnkyWWNCMkM3ZDF6b3lqaHRWNmtCSzlhVmM4ZzAwZWVyL1NwUlEiLCJtYWMiOiJjMTMzNmRlNGE0MjNkMzJkNTY0YzdiMGQyOTU0ZDRkMjM3ZWYwNmY3NTI1NmE4NDNmODA1OTNhNmVmNDc2ODg4IiwidGFnIjoiIn0%3D; osool_session=5TAfNVOWhTeNcLEtmXnzvkLnIXbIpZRJ1jPy3gw7</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-123280802\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-873214746 data-indent-pad=\"  \"><span class=sf-dump-note>array:39</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://osool-b2g.test/dashboards/admin</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IjlHUS9tdTNLRGRyLzA5TG1pZmJuM2c9PSIsInZhbHVlIjoibUFmNFFXWnZwOW1Gb1Era0xxdVB4Qjl4YUt1bnJGV0ZaZ1E4dk9zb2s2SzJOUEFwUklhRUNyNXMzOHgvbkVvMmErL3kxNElKNVNWaGJtT2doeEd4UUZ3V3VKWnkyWWNCMkM3ZDF6b3lqaHRWNmtCSzlhVmM4ZzAwZWVyL1NwUlEiLCJtYWMiOiJjMTMzNmRlNGE0MjNkMzJkNTY0YzdiMGQyOTU0ZDRkMjM3ZWYwNmY3NTI1NmE4NDNmODA1OTNhNmVmNDc2ODg4IiwidGFnIjoiIn0%3D; osool_session=5TAfNVOWhTeNcLEtmXnzvkLnIXbIpZRJ1jPy3gw7</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51706</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/check-crm-workspace</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/check-crm-workspace</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753710258.8507</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753710258</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-873214746\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1887333831 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4C9vQdJyFZ94hAMjhPJDc0YkqTwfCybUFigJMtCN</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1887333831\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1001626903 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 13:44:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlJDMm8xYzFIb0FGYnl6L0hnVVE1SUE9PSIsInZhbHVlIjoiRG44ZVY0U25Pd1dGaUcxTHl4SjFUcUpLOHpHT3hQclcxdU56ZnBwTkhybTJacjBHNVdvUVo4VDR3aENaZHRuMUZqQWNkQXVvMXJBRUJ1MzF1cU9qSlJCemZidDk0RWFNdjJKNDBYMjRkY0Z2TGh3MXFpejQ3bVA4cEYxODZVQlgiLCJtYWMiOiI1OGYwMTE2MTRlYTM4NDI2ZWQ0NWEzMGNjYzJkZTkxYmQ5NWYxNDgzMmQzYjVmNzk1YTI1OTgzM2Q3Y2YxZDI0IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:44:19 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6Ilk1aUdBN1lCRHE3YkFHK1RoczkyZXc9PSIsInZhbHVlIjoiSm4ybVYvM05objVNSWZEWGY4ek15L1FzREdOOXBpTUFvb1cvU01rUWRrUUV6Mnl1TWN1YllzOEdrZlVnK2JVbXR5eVdzRG54S0xLRmtFS3lUb1FtVjNOQ0c0OWJvUjZzVHEzRmZnMmVXNHgvTjJ1WVlyTHpsYjhqcDRKSHBTbnEiLCJtYWMiOiIxYThkZWI2MGUxNzRlMTEwYTdiYzg5YWNhZWYzODNlMDU5Y2Q1ODYzZjIxOWE4ZGM5MDNmY2Q2NThiZmE2YTJjIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:44:19 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlJDMm8xYzFIb0FGYnl6L0hnVVE1SUE9PSIsInZhbHVlIjoiRG44ZVY0U25Pd1dGaUcxTHl4SjFUcUpLOHpHT3hQclcxdU56ZnBwTkhybTJacjBHNVdvUVo4VDR3aENaZHRuMUZqQWNkQXVvMXJBRUJ1MzF1cU9qSlJCemZidDk0RWFNdjJKNDBYMjRkY0Z2TGh3MXFpejQ3bVA4cEYxODZVQlgiLCJtYWMiOiI1OGYwMTE2MTRlYTM4NDI2ZWQ0NWEzMGNjYzJkZTkxYmQ5NWYxNDgzMmQzYjVmNzk1YTI1OTgzM2Q3Y2YxZDI0IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:44:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6Ilk1aUdBN1lCRHE3YkFHK1RoczkyZXc9PSIsInZhbHVlIjoiSm4ybVYvM05objVNSWZEWGY4ek15L1FzREdOOXBpTUFvb1cvU01rUWRrUUV6Mnl1TWN1YllzOEdrZlVnK2JVbXR5eVdzRG54S0xLRmtFS3lUb1FtVjNOQ0c0OWJvUjZzVHEzRmZnMmVXNHgvTjJ1WVlyTHpsYjhqcDRKSHBTbnEiLCJtYWMiOiIxYThkZWI2MGUxNzRlMTEwYTdiYzg5YWNhZWYzODNlMDU5Y2Q1ODYzZjIxOWE4ZGM5MDNmY2Q2NThiZmE2YTJjIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:44:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1001626903\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-384597219 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4C9vQdJyFZ94hAMjhPJDc0YkqTwfCybUFigJMtCN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"80 characters\">http://osool-b2g.test/_debugbar/open?id=Xdd88a3352a372ba0ef80f6e0d6680dac&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>6942</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-384597219\", {\"maxDepth\":0})</script>\n"}}