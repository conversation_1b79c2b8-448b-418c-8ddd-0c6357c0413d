{"__meta": {"id": "Xa39862cb7b756cfb5605cbea04493bf1", "datetime": "2025-07-28 16:51:09", "utime": **********.641043, "method": "GET", "uri": "/user/users-list", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 15, "messages": [{"message": "[16:51:07] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.416594, "xdebug_link": null, "collector": "log"}, {"message": "[16:51:07] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.456052, "xdebug_link": null, "collector": "log"}, {"message": "[16:51:07] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.456131, "xdebug_link": null, "collector": "log"}, {"message": "[16:51:07] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.456234, "xdebug_link": null, "collector": "log"}, {"message": "[16:51:07] LOG.warning: Optional parameter $serviceProviderId declared before required parameter $search is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\UserTrait.php on line 328", "message_html": null, "is_string": false, "label": "warning", "time": **********.458144, "xdebug_link": null, "collector": "log"}, {"message": "[16:51:07] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2418", "message_html": null, "is_string": false, "label": "warning", "time": **********.523739, "xdebug_link": null, "collector": "log"}, {"message": "[16:51:07] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2478", "message_html": null, "is_string": false, "label": "warning", "time": **********.523836, "xdebug_link": null, "collector": "log"}, {"message": "[16:51:07] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3619", "message_html": null, "is_string": false, "label": "warning", "time": **********.524497, "xdebug_link": null, "collector": "log"}, {"message": "[16:51:07] LOG.warning: Optional parameter $filters declared before required parameter $userServiceProvider is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\WorkOrdersTrait.php on line 3418", "message_html": null, "is_string": false, "label": "warning", "time": **********.617313, "xdebug_link": null, "collector": "log"}, {"message": "[16:51:07] LOG.debug: Checking existence of file: uploads/profile_images/**********.png", "message_html": null, "is_string": false, "label": "debug", "time": **********.629566, "xdebug_link": null, "collector": "log"}, {"message": "[16:51:09] LOG.debug: File exists: false", "message_html": null, "is_string": false, "label": "debug", "time": **********.310448, "xdebug_link": null, "collector": "log"}, {"message": "[16:51:09] LOG.debug: Checking existence of file: uploads/profile_images/**********.png", "message_html": null, "is_string": false, "label": "debug", "time": **********.311109, "xdebug_link": null, "collector": "log"}, {"message": "[16:51:09] LOG.debug: File exists: false", "message_html": null, "is_string": false, "label": "debug", "time": **********.54141, "xdebug_link": null, "collector": "log"}, {"message": "[16:51:09] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 637", "message_html": null, "is_string": false, "label": "warning", "time": **********.567053, "xdebug_link": null, "collector": "log"}, {"message": "[16:51:09] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\f8d18d6f63e9bf36e98d3bb1b82c9e7d86b4c71c.php on line 3", "message_html": null, "is_string": false, "label": "warning", "time": **********.591354, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753710666.849316, "end": **********.64108, "duration": 2.7917640209198, "duration_str": "2.79s", "measures": [{"label": "Booting", "start": 1753710666.849316, "relative_start": 0, "end": **********.390783, "relative_end": **********.390783, "duration": 0.5414671897888184, "duration_str": "541ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.390809, "relative_start": 0.5414931774139404, "end": **********.641082, "relative_end": 2.1457672119140625e-06, "duration": 2.2502729892730713, "duration_str": "2.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 59851328, "peak_usage_str": "57MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 10, "templates": [{"name": "applications.admin.user.list-row-view (\\resources\\views\\applications\\admin\\user\\list-row-view.blade.php)", "param_count": 14, "params": ["data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.app (\\resources\\views\\layouts\\app.blade.php)", "param_count": 21, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "value", "key", "loop"], "type": "blade"}, {"name": "layouts.partials._styles (\\resources\\views\\layouts\\partials\\_styles.blade.php)", "param_count": 21, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "value", "key", "loop"], "type": "blade"}, {"name": "layouts.partials._header (\\resources\\views\\layouts\\partials\\_header.blade.php)", "param_count": 21, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "value", "key", "loop"], "type": "blade"}, {"name": "layouts.partials._top_menu (\\resources\\views\\layouts\\partials\\_top_menu.blade.php)", "param_count": 21, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "value", "key", "loop"], "type": "blade"}, {"name": "livewire.notifications.messages-notifications-list (\\resources\\views\\livewire\\notifications\\messages-notifications-list.blade.php)", "param_count": 23, "params": ["chatList", "errors", "_instance", "workspaceSlug", "totalUnreadNotifications", "previousUnreadCount", "newList", "list", "slugs", "userId", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.notifications.new-notifications-list-top-nav (\\resources\\views\\livewire\\notifications\\new-notifications-list-top-nav.blade.php)", "param_count": 28, "params": ["list", "totalUnreadNotifications", "errors", "_instance", "user", "perPage", "assignedAsset", "contractsIds", "accessBuildingsIds", "currentDate", "currentDateTime", "readyToLoad", "configOciLink", "ociLink", "selectedLanguage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.menu.aside-nav-list (\\resources\\views\\livewire\\menu\\aside-nav-list.blade.php)", "param_count": 27, "params": ["userPrivilegesAside", "user", "hasViewPrivilege", "errors", "_instance", "has<PERSON>dmin", "projectId", "project", "workOrderMenuItemColor", "flagWorkorderSidebarMenu", "userPrivileges", "closedWorkOrderCount", "maintenanceRequestCount", "vendorRegistrationApplicationRequests", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.partials._footer (\\resources\\views\\layouts\\partials\\_footer.blade.php)", "param_count": 24, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "value", "key", "loop", "html", "url", "segments"], "type": "blade"}, {"name": "layouts.partials._scripts (\\resources\\views\\layouts\\partials\\_scripts.blade.php)", "param_count": 24, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "value", "key", "loop", "html", "url", "segments"], "type": "blade"}]}, "route": {"uri": "GET user/users-list/{availability_request_id?}/{notification_read?}/{notification_id?}", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Admin\\User\\UserControllerNew@list", "as": "users.list", "namespace": "App\\Http\\Controllers\\Admin\\User", "prefix": "/user", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php&line=112\">\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php:112-250</a>"}, "queries": {"nb_statements": 17, "nb_failed_statements": 0, "accumulated_duration": 0.03773000000000001, "accumulated_duration_str": "37.73ms", "statements": [{"sql": "select * from `users` where `id` = 21 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00501, "duration_str": "5.01ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 13.279}, {"sql": "select count(*) as aggregate from `users` where `user_type` = 'admin' and `project_id` = '191' and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["admin", "191"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php", "line": 219}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00405, "duration_str": "4.05ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php:219", "connection": "osool_test_db", "start_percent": 13.279, "width_percent": 10.734}, {"sql": "select count(*) as aggregate from `users` left join `service_providers` on `service_providers`.`id` = `users`.`service_provider` where ( `service_providers`.`global_sp` = '0' or `service_providers`.`global_sp` IS NULL )  and  ( users.user_type = 'admin' OR\nusers.user_type = 'admin_employee'  OR\n( users.user_type = 'sp_admin' AND service_providers.global_sp = 0 )   OR\nusers.user_type = 'supervisor' OR\nusers.user_type = 'store_keeper' OR\nusers.user_type = 'building_manager' OR\nusers.user_type = 'building_manager_employee' OR\nusers.user_type = 'sp_worker'  )  and `users`.`project_user_id` = 6942 and (`users`.`deleted_at` is null and `users`.`is_deleted` = 'no') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6942", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php", "line": 244}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00656, "duration_str": "6.56ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php:244", "connection": "osool_test_db", "start_percent": 24.013, "width_percent": 17.387}, {"sql": "select * from `worker_availability_request_reason_types`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php", "line": 248}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00289, "duration_str": "2.89ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php:248", "connection": "osool_test_db", "start_percent": 41.399, "width_percent": 7.66}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 191 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["191"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Providers\\AsideViewComposerServiceProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.001, "duration_str": "1ms", "stmt_id": "\\app\\Providers\\AsideViewComposerServiceProvider.php:59", "connection": "osool_test_db", "start_percent": 49.059, "width_percent": 2.65}, {"sql": "select * from `release_notes` where `store_status` = 1 and `release_notes`.`deleted_at` is null group by `version`", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 3048}, {"index": 15, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 51}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.0013700000000000001, "duration_str": "1.37ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:3048", "connection": "osool_test_db", "start_percent": 51.71, "width_percent": 3.631}, {"sql": "select * from `crm_user` where `crm_user`.`user_id` = 21 and `crm_user`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "9740534860d5c11a9cf9e73e8939e4083f82ba1d", "line": 122}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 122}], "duration": 0.00115, "duration_str": "1.15ms", "stmt_id": "view::9740534860d5c11a9cf9e73e8939e4083f82ba1d:122", "connection": "osool_test_db", "start_percent": 55.341, "width_percent": 3.048}, {"sql": "select `name`, `name_ar` from `user_type` where `slug` = 'super_admin' limit 1", "type": "query", "params": [], "bindings": ["super_admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1929}, {"index": 14, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 161}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00125, "duration_str": "1.25ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1929", "connection": "osool_test_db", "start_percent": 58.389, "width_percent": 3.313}, {"sql": "select `project_name`, `project_name_ar` from `projects_details` where `id` = '191' limit 1", "type": "query", "params": [], "bindings": ["191"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 959}, {"index": 14, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 181}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00156, "duration_str": "1.56ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:959", "connection": "osool_test_db", "start_percent": 61.702, "width_percent": 4.135}, {"sql": "select `id`, `project_image`, `use_beneficiary_module`, `use_tenant_module`, `benificiary_status`, `tenant_status`, `project_name`, `project_name_ar`, `use_crm_module` from `projects_details` where `id` = '191' and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["191"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\ProjectDetailTrait.php", "line": 11}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 128}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 71}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "\\app\\Http\\Traits\\ProjectDetailTrait.php:11", "connection": "osool_test_db", "start_percent": 65.836, "width_percent": 2.014}, {"sql": "select `id`, `created_at` from `users` where `project_id` = '191' and `user_type` = 'admin' and `status` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["191", "admin", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\UserTrait.php", "line": 256}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Traits\\UserTrait.php", "line": 267}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 158}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 74}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.00377, "duration_str": "3.77ms", "stmt_id": "\\app\\Http\\Traits\\UserTrait.php:256", "connection": "osool_test_db", "start_percent": 67.851, "width_percent": 9.992}, {"sql": "select count(*) as aggregate from `vendor_profiles` where `submit_status` = 'submit' and `application_status` = 'no_action' and `vendor_profiles`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["submit", "no_action"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 220}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 60}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00182, "duration_str": "1.82ms", "stmt_id": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php:220", "connection": "osool_test_db", "start_percent": 77.843, "width_percent": 4.824}, {"sql": "select exists(select * from `projects_details` where `id` = 191 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["191", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 897}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_test_db", "start_percent": 82.666, "width_percent": 1.776}, {"sql": "select exists(select * from `crm_user` where `user_id` = 21) as `exists`", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 897}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_test_db", "start_percent": 84.442, "width_percent": 1.749}, {"sql": "select `id` from `users` where `project_id` = '191' and `user_type` = 'admin' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["191", "admin", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 928}, {"index": 14, "namespace": "view", "name": "f8d18d6f63e9bf36e98d3bb1b82c9e7d86b4c71c", "line": 9}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00411, "duration_str": "4.11ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:928", "connection": "osool_test_db", "start_percent": 86.191, "width_percent": 10.893}, {"sql": "select exists(select * from `projects_details` where `id` = 191 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["191", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 183}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_test_db", "start_percent": 97.085, "width_percent": 1.882}, {"sql": "select exists(select * from `crm_user` where `user_id` = 21) as `exists`", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 183}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_test_db", "start_percent": 98.966, "width_percent": 1.034}]}, "models": {"data": {"App\\Models\\ReleaseNotes": 2, "App\\Models\\ProjectsDetails": 2, "App\\Models\\WorkerAvailabilityRequestReasonType": 6, "App\\Models\\User": 2}, "count": 12}, "livewire": {"data": {"notifications.messages-notifications-list #ZMC0Ko3C5J6di7gqtUV6": "array:5 [\n  \"data\" => array:7 [\n    \"workspaceSlug\" => \"memo\"\n    \"totalUnreadNotifications\" => 0\n    \"previousUnreadCount\" => 0\n    \"newList\" => null\n    \"list\" => []\n    \"slugs\" => array:3 [\n      0 => \"facebook\"\n      1 => \"whatsapp\"\n      2 => \"instagram\"\n    ]\n    \"userId\" => null\n  ]\n  \"name\" => \"notifications.messages-notifications-list\"\n  \"view\" => \"livewire.notifications.messages-notifications-list\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\MessagesNotificationsList\"\n  \"id\" => \"ZMC0Ko3C5J6di7gqtUV6\"\n]", "notifications.new-notifications-list-top-nav #Lb9HqFGJMTUCO78nCrAU": "array:5 [\n  \"data\" => array:11 [\n    \"user\" => null\n    \"perPage\" => null\n    \"assignedAsset\" => null\n    \"contractsIds\" => null\n    \"accessBuildingsIds\" => null\n    \"currentDate\" => null\n    \"currentDateTime\" => null\n    \"readyToLoad\" => null\n    \"configOciLink\" => null\n    \"ociLink\" => null\n    \"selectedLanguage\" => null\n  ]\n  \"name\" => \"notifications.new-notifications-list-top-nav\"\n  \"view\" => \"livewire.notifications.new-notifications-list-top-nav\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav\"\n  \"id\" => \"Lb9HqFGJMTUCO78nCrAU\"\n]", "menu.aside-nav-list #": "array:7 [\n  \"data\" => array:10 [\n    \"user\" => App\\Models\\User {#3576\n      #connection: \"mysql\"\n      #table: \"users\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:78 [\n        \"id\" => 21\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$JCvJmAzGnnOeoh9po8kYju4db9GhmC.s6.ue6cmFhafhv6MgdUINu\"\n        \"name\" => \"Super Admin\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => \"123456789\"\n        \"profile_img\" => \"**********.png\"\n        \"emp_id\" => \"1234112\"\n        \"profession_id\" => null\n        \"emp_dept\" => \"Management\"\n        \"building_ids\" => null\n        \"contract_ids\" => \"\"\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => \"\"\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => \"1,2,3,4\"\n        \"role_cities\" => \"1,2,3,4\"\n        \"asset_categories\" => \"1,2,3,4\"\n        \"keeper_warehouses\" => null\n        \"properties\" => \"1\"\n        \"contracts\" => \"1\"\n        \"beneficiary\" => \"1\"\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"super_admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 0\n        \"project_id\" => 191\n        \"project_user_id\" => 6942\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2022-03-03 13:52:49\"\n        \"modified_at\" => \"2025-07-28 16:50:21\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"memo\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNzEwNTU2LCJleHAiOjE3NTM3MTQxNTYsIm5iZiI6MTc1MzcxMDU1NiwianRpIjoiS1RSR3dtTXd5cGszNDhzTCIsInN1YiI6IjEiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.F-HytyuBuf52RxpgRuHKyq9INjqBN5U6c4lL2F7q6fI\"\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #original: array:78 [\n        \"id\" => 21\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$JCvJmAzGnnOeoh9po8kYju4db9GhmC.s6.ue6cmFhafhv6MgdUINu\"\n        \"name\" => \"Super Admin\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => \"123456789\"\n        \"profile_img\" => \"**********.png\"\n        \"emp_id\" => \"1234112\"\n        \"profession_id\" => null\n        \"emp_dept\" => \"Management\"\n        \"building_ids\" => null\n        \"contract_ids\" => \"\"\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => \"\"\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => \"1,2,3,4\"\n        \"role_cities\" => \"1,2,3,4\"\n        \"asset_categories\" => \"1,2,3,4\"\n        \"keeper_warehouses\" => null\n        \"properties\" => \"1\"\n        \"contracts\" => \"1\"\n        \"beneficiary\" => \"1\"\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"super_admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 0\n        \"project_id\" => 191\n        \"project_user_id\" => 6942\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2022-03-03 13:52:49\"\n        \"modified_at\" => \"2025-07-28 16:50:21\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"memo\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNzEwNTU2LCJleHAiOjE3NTM3MTQxNTYsIm5iZiI6MTc1MzcxMDU1NiwianRpIjoiS1RSR3dtTXd5cGszNDhzTCIsInN1YiI6IjEiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.F-HytyuBuf52RxpgRuHKyq9INjqBN5U6c4lL2F7q6fI\"\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #changes: []\n      #casts: array:2 [\n        \"email_verified_at\" => \"datetime\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:2 [\n        \"projectDetails\" => App\\Models\\ProjectsDetails {#3917\n          #connection: \"mysql\"\n          #table: \"projects_details\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:23 [\n            \"id\" => 191\n            \"user_id\" => 7034\n            \"project_name\" => \"Testing\"\n            \"project_name_ar\" => \"Testing23\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => \"20250427115740146654.jpg\"\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2025-02-18 17:58:25\"\n            \"updated_at\" => \"2025-07-28 15:47:26\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 0\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 0\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => null\n            \"contract_end_date\" => null\n            \"share_post\" => 1\n            \"deleted_at\" => null\n            \"crm_workspace_slug\" => \"testing23\"\n          ]\n          #original: array:23 [\n            \"id\" => 191\n            \"user_id\" => 7034\n            \"project_name\" => \"Testing\"\n            \"project_name_ar\" => \"Testing23\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => \"20250427115740146654.jpg\"\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2025-02-18 17:58:25\"\n            \"updated_at\" => \"2025-07-28 15:47:26\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 0\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 0\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => null\n            \"contract_end_date\" => null\n            \"share_post\" => 1\n            \"deleted_at\" => null\n            \"crm_workspace_slug\" => \"testing23\"\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:16 [\n            0 => \"user_id\"\n            1 => \"project_name\"\n            2 => \"project_name_ar\"\n            3 => \"project_image\"\n            4 => \"industry_type\"\n            5 => \"created_by\"\n            6 => \"is_deleted\"\n            7 => \"use_erp_module\"\n            8 => \"use_tenant_module\"\n            9 => \"tenant_status\"\n            10 => \"use_beneficiary_module\"\n            11 => \"benificiary_status\"\n            12 => \"community_status\"\n            13 => \"contract_status\"\n            14 => \"share_post\"\n            15 => \"use_crm_module\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n          #excludedAttributes: []\n          +auditEvent: null\n          +auditCustomOld: null\n          +auditCustomNew: null\n          +isCustomEvent: false\n          +preloadedResolverData: []\n        }\n        \"crmUser\" => null\n      ]\n      #touches: []\n      +timestamps: true\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: array:62 [\n        0 => \"allow_akaunting\"\n        1 => \"email\"\n        2 => \"password\"\n        3 => \"name\"\n        4 => \"first_name\"\n        5 => \"last_name\"\n        6 => \"apartment\"\n        7 => \"unit_receival_date\"\n        8 => \"later_booking_alert\"\n        9 => \"phone\"\n        10 => \"profile_img\"\n        11 => \"address\"\n        12 => \"country_id\"\n        13 => \"city_id\"\n        14 => \"role_regions\"\n        15 => \"role_cities\"\n        16 => \"asset_categories\"\n        17 => \"properties\"\n        18 => \"contracts\"\n        19 => \"beneficiary\"\n        20 => \"service_provider\"\n        21 => \"user_type\"\n        22 => \"project_id\"\n        23 => \"project_user_id\"\n        24 => \"created_by\"\n        25 => \"status\"\n        26 => \"user_privileges\"\n        27 => \"approved_max_amount\"\n        28 => \"emp_id\"\n        29 => \"profession_id\"\n        30 => \"emp_dept\"\n        31 => \"building_ids\"\n        32 => \"contract_ids\"\n        33 => \"supervisor_id\"\n        34 => \"sp_admin_id\"\n        35 => \"langForSms\"\n        36 => \"deleted_at\"\n        37 => \"otp\"\n        38 => \"temp_password\"\n        39 => \"otp_for_password\"\n        40 => \"otp_for_password_verified\"\n        41 => \"temp_phone_number\"\n        42 => \"favorite_language\"\n        43 => \"is_subcontractors_worker\"\n        44 => \"keeper_warehouses\"\n        45 => \"save_later_date\"\n        46 => \"first_login\"\n        47 => \"is_unit_link\"\n        48 => \"akaunting_vendor_id\"\n        49 => \"akaunting_customer_id\"\n        50 => \"crm_api_token\"\n        51 => \"workspace_slug\"\n        52 => \"is_bma_area_manager\"\n        53 => \"assigned_workers\"\n        54 => \"sleep_mode\"\n        55 => \"offline_mode\"\n        56 => \"attendance_mandatory\"\n        57 => \"admin_level\"\n        58 => \"role\"\n        59 => \"attendance_target\"\n        60 => \"salary\"\n        61 => \"show_extra_info\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #rememberTokenName: \"remember_token\"\n      #accessToken: null\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n    }\n    \"hasAdmin\" => 6942\n    \"projectId\" => \"191\"\n    \"project\" => App\\Models\\ProjectsDetails {#4336\n      #connection: \"mysql\"\n      #table: \"projects_details\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:9 [\n        \"id\" => 191\n        \"project_image\" => \"20250427115740146654.jpg\"\n        \"use_beneficiary_module\" => 0\n        \"use_tenant_module\" => 0\n        \"benificiary_status\" => 1\n        \"tenant_status\" => 1\n        \"project_name\" => \"Testing\"\n        \"project_name_ar\" => \"Testing23\"\n        \"use_crm_module\" => 1\n      ]\n      #original: array:9 [\n        \"id\" => 191\n        \"project_image\" => \"20250427115740146654.jpg\"\n        \"use_beneficiary_module\" => 0\n        \"use_tenant_module\" => 0\n        \"benificiary_status\" => 1\n        \"tenant_status\" => 1\n        \"project_name\" => \"Testing\"\n        \"project_name_ar\" => \"Testing23\"\n        \"use_crm_module\" => 1\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:16 [\n        0 => \"user_id\"\n        1 => \"project_name\"\n        2 => \"project_name_ar\"\n        3 => \"project_image\"\n        4 => \"industry_type\"\n        5 => \"created_by\"\n        6 => \"is_deleted\"\n        7 => \"use_erp_module\"\n        8 => \"use_tenant_module\"\n        9 => \"tenant_status\"\n        10 => \"use_beneficiary_module\"\n        11 => \"benificiary_status\"\n        12 => \"community_status\"\n        13 => \"contract_status\"\n        14 => \"share_post\"\n        15 => \"use_crm_module\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n    }\n    \"workOrderMenuItemColor\" => null\n    \"flagWorkorderSidebarMenu\" => false\n    \"userPrivileges\" => null\n    \"closedWorkOrderCount\" => null\n    \"maintenanceRequestCount\" => 0\n    \"vendorRegistrationApplicationRequests\" => 2\n  ]\n  \"oldData\" => null\n  \"actionQueue\" => null\n  \"name\" => \"menu.aside-nav-list\"\n  \"view\" => \"livewire.menu.aside-nav-list\"\n  \"component\" => \"App\\Http\\Livewire\\Menu\\AsideNavList\"\n  \"id\" => null\n]"}, "count": 3}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"locale": "en", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/user/users-list\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "21", "plain_user_password": "Tarqeem21", "entered_project_id": "191", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/user/users-list", "status_code": "<pre class=sf-dump id=sf-dump-1191645557 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1191645557\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1495494670 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1495494670\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2031811728 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2031811728\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-223458618 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"237 characters\">http://osool-b2g.test/user/eyJpdiI6Ilovc05wWmNQRHBnT0VmeTA2WHIzVkE9PSIsInZhbHVlIjoiTWI1eUZWcDlYM0tNTDVDM0lFNDc1Zz09IiwibWFjIjoiYTdhMTkzZGExMTc4YjI0YzJlODE2YWI5YTdkMjVkMzE5OTU3NGUyMDNmMDg4NTFjOGZmODM4MjNlM2FlZThlNCIsInRhZyI6IiJ9/edit/role</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6InpPUTJVWmsvUGJiM2lsUStjbThnRlE9PSIsInZhbHVlIjoiSUJadUdTclI1cWJkTmpZMFRId3E5V05aWSsyeUg1dGhhQWlNR3paejVaQ2d3cVBMRExOdDZuNDByVDA1U1M0N3FoM2lwZWIrQXdzRFIrVFo3NUw4cUplK3VVOGo3TEk0aytKOUc2YXVVd01zSGVFMkg4MERsTVVHSENkR1BlblciLCJtYWMiOiJjNzA5NDg4MWNhOTdjYTY4NGIxYjg1M2U1MzQ5MmVlYWZlODAxMTFjMDQ4MTMxMGNkMTRkNzljMDJjZGMyOGVlIiwidGFnIjoiIn0%3D; osool_session=HDWHbyLPkYKSwTFu4VBDKslDUrIVvku1jpatlu4r</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-223458618\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-2439900 data-indent-pad=\"  \"><span class=sf-dump-note>array:39</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"237 characters\">http://osool-b2g.test/user/eyJpdiI6Ilovc05wWmNQRHBnT0VmeTA2WHIzVkE9PSIsInZhbHVlIjoiTWI1eUZWcDlYM0tNTDVDM0lFNDc1Zz09IiwibWFjIjoiYTdhMTkzZGExMTc4YjI0YzJlODE2YWI5YTdkMjVkMzE5OTU3NGUyMDNmMDg4NTFjOGZmODM4MjNlM2FlZThlNCIsInRhZyI6IiJ9/edit/role</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6InpPUTJVWmsvUGJiM2lsUStjbThnRlE9PSIsInZhbHVlIjoiSUJadUdTclI1cWJkTmpZMFRId3E5V05aWSsyeUg1dGhhQWlNR3paejVaQ2d3cVBMRExOdDZuNDByVDA1U1M0N3FoM2lwZWIrQXdzRFIrVFo3NUw4cUplK3VVOGo3TEk0aytKOUc2YXVVd01zSGVFMkg4MERsTVVHSENkR1BlblciLCJtYWMiOiJjNzA5NDg4MWNhOTdjYTY4NGIxYjg1M2U1MzQ5MmVlYWZlODAxMTFjMDQ4MTMxMGNkMTRkNzljMDJjZGMyOGVlIiwidGFnIjoiIn0%3D; osool_session=HDWHbyLPkYKSwTFu4VBDKslDUrIVvku1jpatlu4r</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52606</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/user/users-list</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/user/users-list</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753710666.8493</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753710666</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2439900\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-287939324 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-287939324\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2055898649 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 13:51:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlBJcXAwcE1pK3FhMHg1dkQrSTVuZ3c9PSIsInZhbHVlIjoiMG1HNWNOR0FkR3JqcldMNmVNM04vK3NvUzBHeDNmaTEvZVo2UFZ3TjZYQWdlOTlLUVVYVmRKYXI5eEQ5ZkROQTlXYWJYS2dwaW1YdjBudVRnbTJsMGZQWEZHQnA1NVZZYW56eTByR0RxdW1HSEg4UjkyOVltU3dKSjFmbG04MloiLCJtYWMiOiJjZWZiYWRmMzMxMzIzYWQ1Nzg5ZjE2N2Q3NDBiNWE4MTRlOGNlOTVkOTVmYjNhNjI5ODc2OTk4MzE0MWFjZmVlIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:51:09 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6ImgxclZvR1dlRnlZbDVidzlUQXVlc2c9PSIsInZhbHVlIjoib3ozNDlNblJSdzUwdkV0dXk5Q0hBalBnVHEyck5JSk9jc2xqNHhUa1dyeDUxbkwwRzJ5NGU2bktDdmUxQ2JKOTBBanBHWXBOb3NrTjBSWG5ZWXdaNUMwSmlBZmtRbEJVOGNwZ1R1STFYM2hlUDlyT3BxTVE1bUV5OVJOMUlTUWMiLCJtYWMiOiIwMjczZTYyOTdlOGIxZDU4YTMxODc3N2Y2ZDc2OGYwNTY1NzhmODFmMzI4Y2Q2MzczZjQ5MmIzNmZjNmQ4Y2JhIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:51:09 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlBJcXAwcE1pK3FhMHg1dkQrSTVuZ3c9PSIsInZhbHVlIjoiMG1HNWNOR0FkR3JqcldMNmVNM04vK3NvUzBHeDNmaTEvZVo2UFZ3TjZYQWdlOTlLUVVYVmRKYXI5eEQ5ZkROQTlXYWJYS2dwaW1YdjBudVRnbTJsMGZQWEZHQnA1NVZZYW56eTByR0RxdW1HSEg4UjkyOVltU3dKSjFmbG04MloiLCJtYWMiOiJjZWZiYWRmMzMxMzIzYWQ1Nzg5ZjE2N2Q3NDBiNWE4MTRlOGNlOTVkOTVmYjNhNjI5ODc2OTk4MzE0MWFjZmVlIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:51:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6ImgxclZvR1dlRnlZbDVidzlUQXVlc2c9PSIsInZhbHVlIjoib3ozNDlNblJSdzUwdkV0dXk5Q0hBalBnVHEyck5JSk9jc2xqNHhUa1dyeDUxbkwwRzJ5NGU2bktDdmUxQ2JKOTBBanBHWXBOb3NrTjBSWG5ZWXdaNUMwSmlBZmtRbEJVOGNwZ1R1STFYM2hlUDlyT3BxTVE1bUV5OVJOMUlTUWMiLCJtYWMiOiIwMjczZTYyOTdlOGIxZDU4YTMxODc3N2Y2ZDc2OGYwNTY1NzhmODFmMzI4Y2Q2MzczZjQ5MmIzNmZjNmQ4Y2JhIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:51:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2055898649\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1163182276 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://osool-b2g.test/user/users-list</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>21</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Tarqeem21</span>\"\n  \"<span class=sf-dump-key>entered_project_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">191</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1163182276\", {\"maxDepth\":0})</script>\n"}}