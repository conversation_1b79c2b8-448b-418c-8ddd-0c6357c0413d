{"__meta": {"id": "X07c88453544ce5edf50b37a5219571a3", "datetime": "2025-07-28 16:50:21", "utime": 1753710621.234392, "method": "GET", "uri": "/workspace/enter/eyJpdiI6IkF6K1Erd0hpcG11K3krOE9ldHRsWlE9PSIsInZhbHVlIjoiY3REVEdibzRENXpCZllFb1RyNlREUT09IiwibWFjIjoiYTdmOThlYmQzYTM2NTRkOTc3Nzg5ODkyN2NmZmRmOWY0NjIyNzgzMDkyMTZjOWNhYWVjZGJhZTFlOWI5NjQwNyIsInRhZyI6IiJ9", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 4, "messages": [{"message": "[16:50:21] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753710621.118371, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:21] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2418", "message_html": null, "is_string": false, "label": "warning", "time": 1753710621.181991, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:21] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2478", "message_html": null, "is_string": false, "label": "warning", "time": 1753710621.182092, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:21] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3619", "message_html": null, "is_string": false, "label": "warning", "time": 1753710621.182595, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753710620.638999, "end": 1753710621.234416, "duration": 0.5954170227050781, "duration_str": "595ms", "measures": [{"label": "Booting", "start": 1753710620.638999, "relative_start": 0, "end": 1753710621.095826, "relative_end": 1753710621.095826, "duration": 0.45682692527770996, "duration_str": "457ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753710621.095851, "relative_start": 0.4568519592285156, "end": 1753710621.234418, "relative_end": 1.9073486328125e-06, "duration": 0.1385669708251953, "duration_str": "139ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 37940000, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET workspace/enter/{id}", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController@selectproject", "as": "workspace.enter", "namespace": "App\\Http\\Controllers\\Admin\\Workspace", "prefix": "/workspace", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php&line=293\">\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:293-331</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02994, "accumulated_duration_str": "29.94ms", "statements": [{"sql": "select * from `users` where `id` = 21 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.004030000000000001, "duration_str": "4.03ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 13.46}, {"sql": "select * from `users` where `project_id` = '191' and `user_type` = 'admin' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["191", "admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 306}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.01202, "duration_str": "12.02ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:306", "connection": "osool_test_db", "start_percent": 13.46, "width_percent": 40.147}, {"sql": "select * from `projects_details` where `projects_details`.`id` = '191' and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["191"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\User.php", "line": 209}, {"index": 21, "namespace": null, "name": "\\app\\Models\\User.php", "line": 200}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 313}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "\\app\\Models\\User.php:209", "connection": "osool_test_db", "start_percent": 53.607, "width_percent": 2.538}, {"sql": "update `users` set `project_id` = '191', `project_user_id` = 6942, `allow_akaunting` = 1, `users`.`modified_at` = '2025-07-28 16:50:21' where `id` = 21", "type": "query", "params": [], "bindings": ["191", "6942", "1", "2025-07-28 16:50:21", "21"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 313}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00441, "duration_str": "4.41ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:313", "connection": "osool_test_db", "start_percent": 56.146, "width_percent": 14.729}, {"sql": "insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('{\\\"project_id\\\":0,\\\"project_user_id\\\":0,\\\"allow_akaunting\\\":0}', '{\\\"project_id\\\":\\\"191\\\",\\\"project_user_id\\\":6942,\\\"allow_akaunting\\\":1}', 'updated', 21, 'App\\Models\\User', 21, 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'http://osool-b2g.test/workspace/enter/eyJpdiI6IkF6K1Erd0hpcG11K3krOE9ldHRsWlE9PSIsInZhbHVlIjoiY3REVEdibzRENXpCZllFb1RyNlREUT09IiwibWFjIjoiYTdmOThlYmQzYTM2NTRkOTc3Nzg5ODkyN2NmZmRmOWY0NjIyNzgzMDkyMTZjOWNhYWVjZGJhZTFlOWI5NjQwNyIsInRhZyI6IiJ9', '2025-07-28 16:50:21', '2025-07-28 16:50:21')", "type": "query", "params": [], "bindings": ["{&quot;project_id&quot;:0,&quot;project_user_id&quot;:0,&quot;allow_akaunting&quot;:0}", "{&quot;project_id&quot;:&quot;191&quot;,&quot;project_user_id&quot;:6942,&quot;allow_akaunting&quot;:1}", "updated", "21", "App\\Models\\User", "21", "App\\Models\\User", "", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "http://osool-b2g.test/workspace/enter/eyJpdiI6IkF6K1Erd0hpcG11K3krOE9ldHRsWlE9PSIsInZhbHVlIjoiY3REVEdibzRENXpCZllFb1RyNlREUT09IiwibWFjIjoiYTdmOThlYmQzYTM2NTRkOTc3Nzg5ODkyN2NmZmRmOWY0NjIyNzgzMDkyMTZjOWNhYWVjZGJhZTFlOWI5NjQwNyIsInRhZyI6IiJ9", "2025-07-28 16:50:21", "2025-07-28 16:50:21"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "\\vendor\\owen-it\\laravel-auditing\\src\\Auditor.php", "line": 83}, {"index": 25, "namespace": null, "name": "\\vendor\\owen-it\\laravel-auditing\\src\\AuditableObserver.php", "line": 99}, {"index": 26, "namespace": null, "name": "\\vendor\\owen-it\\laravel-auditing\\src\\AuditableObserver.php", "line": 49}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 313}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00584, "duration_str": "5.84ms", "stmt_id": "\\vendor\\owen-it\\laravel-auditing\\src\\Auditor.php:83", "connection": "osool_test_db", "start_percent": 70.875, "width_percent": 19.506}, {"sql": "select count(*) as aggregate from `properties` where `user_id` = 6942 and `user_id` != 0", "type": "query", "params": [], "bindings": ["6942", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 944}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 322}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0028799999999999997, "duration_str": "2.88ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:944", "connection": "osool_test_db", "start_percent": 90.381, "width_percent": 9.619}]}, "models": {"data": {"App\\Models\\ProjectsDetails": 1, "App\\Models\\User": 2}, "count": 3}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"locale": "en", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/workspace/enter/eyJpdiI6IkF6K1Erd0hpcG11K3krOE9ldHRsWlE9PSIsInZhbHVlIjoiY3REVEdibzRENXpCZllFb1RyNlREUT09IiwibWFjIjoiYTdmOThlYmQzYTM2NTRkOTc3Nzg5ODkyN2NmZmRmOWY0NjIyNzgzMDkyMTZjOWNhYWVjZGJhZTFlOWI5NjQwNyIsInRhZyI6IiJ9\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "21", "plain_user_password": "Tarqeem21", "PHPDEBUGBAR_STACK_DATA": "[]", "entered_project_id": "191"}, "request": {"path_info": "/workspace/enter/eyJpdiI6IkF6K1Erd0hpcG11K3krOE9ldHRsWlE9PSIsInZhbHVlIjoiY3REVEdibzRENXpCZllFb1RyNlREUT09IiwibWFjIjoiYTdmOThlYmQzYTM2NTRkOTc3Nzg5ODkyN2NmZmRmOWY0NjIyNzgzMDkyMTZjOWNhYWVjZGJhZTFlOWI5NjQwNyIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-922927944 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-922927944\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-573987949 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-573987949\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-157020157 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-157020157\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/workspace/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6Ikd1ZlFDOUk2MXZ3Tm5yRHdsNUVVSkE9PSIsInZhbHVlIjoidnBSdTI3MGFGSVUydSs4eFVheFg0MEsybHF5KzhPZUZnV21tS2srQXY0aVZpK0VCYTl2Q0dRRU0xMWVHVnJIME9FTG9vMHIwZFE5cjNHbHBGc2xwNVNHSzROTEYrVUNVMTF6clU1RDJNaWhPYVlZalJOL0hqYVAxbkkvRDBLS3MiLCJtYWMiOiI1ZDlkYWQ0N2I0Y2YyMTg0MzFjNmFjZjIzNGJlY2Q3MWI1OTg1ZTViNjQyM2I0OTcwMjc5ZDQwMzk1MTIzYzc2IiwidGFnIjoiIn0%3D; osool_session=RYMa3n0k4KUZhnobk9RxVN3EP1DjZHW5uUsBJPbI</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-609286785 data-indent-pad=\"  \"><span class=sf-dump-note>array:39</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/workspace/projects</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6Ikd1ZlFDOUk2MXZ3Tm5yRHdsNUVVSkE9PSIsInZhbHVlIjoidnBSdTI3MGFGSVUydSs4eFVheFg0MEsybHF5KzhPZUZnV21tS2srQXY0aVZpK0VCYTl2Q0dRRU0xMWVHVnJIME9FTG9vMHIwZFE5cjNHbHBGc2xwNVNHSzROTEYrVUNVMTF6clU1RDJNaWhPYVlZalJOL0hqYVAxbkkvRDBLS3MiLCJtYWMiOiI1ZDlkYWQ0N2I0Y2YyMTg0MzFjNmFjZjIzNGJlY2Q3MWI1OTg1ZTViNjQyM2I0OTcwMjc5ZDQwMzk1MTIzYzc2IiwidGFnIjoiIn0%3D; osool_session=RYMa3n0k4KUZhnobk9RxVN3EP1DjZHW5uUsBJPbI</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52462</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"217 characters\">/workspace/enter/eyJpdiI6IkF6K1Erd0hpcG11K3krOE9ldHRsWlE9PSIsInZhbHVlIjoiY3REVEdibzRENXpCZllFb1RyNlREUT09IiwibWFjIjoiYTdmOThlYmQzYTM2NTRkOTc3Nzg5ODkyN2NmZmRmOWY0NjIyNzgzMDkyMTZjOWNhYWVjZGJhZTFlOWI5NjQwNyIsInRhZyI6IiJ9</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"217 characters\">/workspace/enter/eyJpdiI6IkF6K1Erd0hpcG11K3krOE9ldHRsWlE9PSIsInZhbHVlIjoiY3REVEdibzRENXpCZllFb1RyNlREUT09IiwibWFjIjoiYTdmOThlYmQzYTM2NTRkOTc3Nzg5ODkyN2NmZmRmOWY0NjIyNzgzMDkyMTZjOWNhYWVjZGJhZTFlOWI5NjQwNyIsInRhZyI6IiJ9</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753710620.639</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753710620</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-609286785\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2079384968 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2079384968\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1336318657 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 13:50:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://osool-b2g.test/dashboards/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkZYZ1FjZld5a0l6Y1VuUGorOXA1RFE9PSIsInZhbHVlIjoiYzRCYXRweEdpc2loSTRsRmNkbFJzYVcrcjNEVHlmeGtzRWhyZk96V05rY2xJRklsVS91RHVkNHN6UDdQU3ZjdUhzbFMxWVpQVFVTWnZ0QVdRd21QZ1ZxWVhuUnJ0V0o5RFN0dkxHcE5UVTl3Syt0MzQ2ZlVuOVBzQ3ozc2tvK1oiLCJtYWMiOiIxODM3MjI0OTRlZjc5ZGNlMzJiZmVhYjhjNmY3NzFjMjI5YTcyZjc3N2ViNWZhYWJiMDU1NGJmMzNhMWUwYzI3IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:50:21 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6Ingwd2NZTzJJM2owU2NPSUhCYlB2TFE9PSIsInZhbHVlIjoiVDNtK0ZrS1RZU0FsaDh1QS9vTldYWFd0Q0ZaOG9yTmNmY1AvMzhaTWFaQ0hsVi9QcVlpb3dRTTlGTnA4blRpb2hCMlJmKzFFWloyQVhLd043bU5ERVNjdmppekxjMHlKTkZuOHViTDhBTjE2RUVSN2J5YUJEL3o3V1FNc240WWwiLCJtYWMiOiI1M2ZjNDAyODBkMmU5N2MwYzc0MThiMDA5M2YwYmU0NTNlNjY5YmMxOGI2YWMzNjM3MGFhYTI4YTBhMjRiNGU1IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:50:21 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkZYZ1FjZld5a0l6Y1VuUGorOXA1RFE9PSIsInZhbHVlIjoiYzRCYXRweEdpc2loSTRsRmNkbFJzYVcrcjNEVHlmeGtzRWhyZk96V05rY2xJRklsVS91RHVkNHN6UDdQU3ZjdUhzbFMxWVpQVFVTWnZ0QVdRd21QZ1ZxWVhuUnJ0V0o5RFN0dkxHcE5UVTl3Syt0MzQ2ZlVuOVBzQ3ozc2tvK1oiLCJtYWMiOiIxODM3MjI0OTRlZjc5ZGNlMzJiZmVhYjhjNmY3NzFjMjI5YTcyZjc3N2ViNWZhYWJiMDU1NGJmMzNhMWUwYzI3IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:50:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6Ingwd2NZTzJJM2owU2NPSUhCYlB2TFE9PSIsInZhbHVlIjoiVDNtK0ZrS1RZU0FsaDh1QS9vTldYWFd0Q0ZaOG9yTmNmY1AvMzhaTWFaQ0hsVi9QcVlpb3dRTTlGTnA4blRpb2hCMlJmKzFFWloyQVhLd043bU5ERVNjdmppekxjMHlKTkZuOHViTDhBTjE2RUVSN2J5YUJEL3o3V1FNc240WWwiLCJtYWMiOiI1M2ZjNDAyODBkMmU5N2MwYzc0MThiMDA5M2YwYmU0NTNlNjY5YmMxOGI2YWMzNjM3MGFhYTI4YTBhMjRiNGU1IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:50:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1336318657\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-729097683 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"238 characters\">http://osool-b2g.test/workspace/enter/eyJpdiI6IkF6K1Erd0hpcG11K3krOE9ldHRsWlE9PSIsInZhbHVlIjoiY3REVEdibzRENXpCZllFb1RyNlREUT09IiwibWFjIjoiYTdmOThlYmQzYTM2NTRkOTc3Nzg5ODkyN2NmZmRmOWY0NjIyNzgzMDkyMTZjOWNhYWVjZGJhZTFlOWI5NjQwNyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>21</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Tarqeem21</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>entered_project_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">191</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-729097683\", {\"maxDepth\":0})</script>\n"}}