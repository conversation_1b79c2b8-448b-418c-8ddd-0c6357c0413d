{"__meta": {"id": "Xb8763404889e75fed14af3305f7e1ff2", "datetime": "2025-07-28 16:50:28", "utime": **********.854408, "method": "POST", "uri": "/livewire/message/notifications.new-notifications-list-top-nav", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 103, "messages": [{"message": "[16:50:26] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.438579, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:26] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.560572, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:26] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.560679, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:26] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.560721, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:26] LOG.warning: Optional parameter $filters declared before required parameter $userServiceProvider is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\WorkOrdersTrait.php on line 3418", "message_html": null, "is_string": false, "label": "warning", "time": **********.663141, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:26] LOG.warning: Optional parameter $serviceProviderId declared before required parameter $search is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\UserTrait.php on line 328", "message_html": null, "is_string": false, "label": "warning", "time": **********.684819, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.820794, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.820859, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.820901, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.820959, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.820999, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821034, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821069, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821144, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821224, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.82127, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821326, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821374, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821424, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821462, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821498, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.82154, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821575, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821609, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821643, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821676, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821713, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821747, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821781, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821814, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821847, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.82188, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821913, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821946, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.821991, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822026, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822059, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822091, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822124, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822157, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822189, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822222, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822254, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822293, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822335, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822367, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822433, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822471, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822508, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822554, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822588, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.82262, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822655, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822688, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.82272, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822752, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822784, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822816, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822848, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822881, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822922, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822961, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.822996, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823029, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823061, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823093, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823126, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823158, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.82319, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823221, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823256, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823288, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823321, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823353, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823385, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823417, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823449, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823481, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823513, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823545, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823615, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.82369, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823733, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823773, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.82381, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823845, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823881, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823915, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823953, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.823989, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.824023, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.824057, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.8241, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.824134, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.824179, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.824212, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.824245, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.824291, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.824329, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.824379, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.824412, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.824445, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:28] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 74", "message_html": null, "is_string": false, "label": "warning", "time": **********.824477, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753710625.045349, "end": **********.855022, "duration": 3.8096730709075928, "duration_str": "3.81s", "measures": [{"label": "Booting", "start": 1753710625.045349, "relative_start": 0, "end": **********.387275, "relative_end": **********.387275, "duration": 1.341926097869873, "duration_str": "1.34s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.387291, "relative_start": 1.3419420719146729, "end": **********.855028, "relative_end": 5.9604644775390625e-06, "duration": 2.4677369594573975, "duration_str": "2.47s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 39952192, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "livewire.notifications.new-notifications-list-top-nav (\\resources\\views\\livewire\\notifications\\new-notifications-list-top-nav.blade.php)", "param_count": 28, "params": ["list", "totalUnreadNotifications", "errors", "_instance", "user", "perPage", "assignedAsset", "contractsIds", "accessBuildingsIds", "currentDate", "currentDateTime", "readyToLoad", "configOciLink", "ociLink", "selectedLanguage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 12, "nb_failed_statements": 0, "accumulated_duration": 2.0687699999999998, "accumulated_duration_str": "2.07s", "statements": [{"sql": "select * from `users` where `id` = 21 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 23}], "duration": 0.00619, "duration_str": "6.19ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 0.299}, {"sql": "select `asset_id` from `user_assets_mapping` where `user_id` = 21", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Traits\\UserAssetMappingTrait.php", "line": 11}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 62}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 202}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.0025099999999999996, "duration_str": "2.51ms", "stmt_id": "\\app\\Http\\Traits\\UserAssetMappingTrait.php:11", "connection": "osool_test_db", "start_percent": 0.299, "width_percent": 0.121}, {"sql": "select `contracts`.`id` from `contracts` where `contracts`.`is_deleted` = 'no' and `contracts`.`deleted_at` is null and `contracts`.`user_id` = 6942 and `contracts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["no", "6942"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Traits\\ContractsTrait.php", "line": 160}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 74}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 203}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.0015400000000000001, "duration_str": "1.54ms", "stmt_id": "\\app\\Http\\Traits\\ContractsTrait.php:160", "connection": "osool_test_db", "start_percent": 0.421, "width_percent": 0.074}, {"sql": "select `property_buildings`.`id` from `properties` left join `property_buildings` on `property_buildings`.`property_id` = `properties`.`id` where `properties`.`user_id` = 6942 and `properties`.`is_deleted` = 'no' and `properties`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6942", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Traits\\PropertyTrait.php", "line": 317}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 103}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 204}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00588, "duration_str": "5.88ms", "stmt_id": "\\app\\Http\\Traits\\PropertyTrait.php:317", "connection": "osool_test_db", "start_percent": 0.495, "width_percent": 0.284}, {"sql": "select `id_configuration`, `name`, `code`, `value`, `description`, `active`, `platform` from `configurations` where `code` = 3 and `configurations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\ConfigurationTrait.php", "line": 49}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 167}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 205}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.0035099999999999997, "duration_str": "3.51ms", "stmt_id": "\\app\\Http\\Traits\\ConfigurationTrait.php:49", "connection": "osool_test_db", "start_percent": 0.779, "width_percent": 0.17}, {"sql": "select `asset_id` from `user_assets_mapping` where `user_id` = 21", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Traits\\UserAssetMappingTrait.php", "line": 11}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 62}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 149}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 207}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.00159, "duration_str": "1.59ms", "stmt_id": "\\app\\Http\\Traits\\UserAssetMappingTrait.php:11", "connection": "osool_test_db", "start_percent": 0.949, "width_percent": 0.077}, {"sql": "select `contracts`.`id` from `contracts` where `contracts`.`is_deleted` = 'no' and `contracts`.`deleted_at` is null and `contracts`.`user_id` = 6942 and `contracts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["no", "6942"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Traits\\ContractsTrait.php", "line": 160}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 74}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 207}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "\\app\\Http\\Traits\\ContractsTrait.php:160", "connection": "osool_test_db", "start_percent": 1.026, "width_percent": 0.031}, {"sql": "select `property_buildings`.`id` from `properties` left join `property_buildings` on `property_buildings`.`property_id` = `properties`.`id` where `properties`.`user_id` = 6942 and `properties`.`is_deleted` = 'no' and `properties`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6942", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Traits\\PropertyTrait.php", "line": 317}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 103}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 207}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.00387, "duration_str": "3.87ms", "stmt_id": "\\app\\Http\\Traits\\PropertyTrait.php:317", "connection": "osool_test_db", "start_percent": 1.057, "width_percent": 0.187}, {"sql": "select `id_configuration`, `name`, `code`, `value`, `description`, `active`, `platform` from `configurations` where `code` = 3 and `configurations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\ConfigurationTrait.php", "line": 49}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 167}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 154}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 207}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.01326, "duration_str": "13.26ms", "stmt_id": "\\app\\Http\\Traits\\ConfigurationTrait.php:49", "connection": "osool_test_db", "start_percent": 1.244, "width_percent": 0.641}, {"sql": "select `notifications`.*, `work_orders`.`work_order_id`, `work_orders`.`work_order_type`, `work_orders`.`assigned_to`, `work_orders`.`supervisor_id` from `notifications` left join `work_orders` on `work_orders`.`id` = `notifications`.`section_id` left join `user_assets_mapping` on `user_assets_mapping`.`contract_id` = `work_orders`.`contract_id` where (`notifications`.`is_timeline` = 'no' or `notifications`.`is_timeline` is null) and (NOT find_in_set(21, notifications.user_id)) and (`notifications`.`building_ids` in (6056) and `user_assets_mapping`.`contract_id` in (407, 408) and `notifications`.`notification_sub_type` in ('new_work_order_created', 'bm_has_approved_and_evaluated_wo', 'wo_completed_wo', 'bm_work_order_rejected', 'bm_has_did_not_agreed_on_workorder', 'bm_has_reopend_wo', 'wo_started_wo', 'sent_to_project_owner') and `notifications`.`section_type` = 'work_order' and `notifications`.`section_type` != 'new_chat_message' and `notifications`.`created_at` between '2025-06-28 16:50:26' and '2025-07-28 16:50:26') or (`notifications`.`section_type` = 'report' and `notifications`.`user_id` = 21 and `notifications`.`created_at` between '2025-06-28 16:50:26' and '2025-07-28 16:50:26') or (`notifications`.`section_type` = 'contracts' and FIND_IN_SET(21, notifications.user_id) and `notifications`.`created_at` between '2025-06-28 16:50:26' and '2025-07-28 16:50:26') or (`notifications`.`section_type` = 'variation_order' and FIND_IN_SET( 21, notifications.user_id) and `notifications`.`created_at` between '2025-06-28 16:50:26' and '2025-07-28 16:50:26') or (`notifications`.`section_type` = 'advance_contracts' and FIND_IN_SET(21, notifications.user_id) and `notifications`.`created_at` between '2025-06-28 16:50:26' and '2025-07-28 16:50:26') or (`notifications`.`section_type` = 'complaints' and FIND_IN_SET(21, notifications.user_id) and `notifications`.`created_at` between '2025-06-28 16:50:26' and '2025-07-28 16:50:26') group by `notifications`.`id` order by `notifications`.`created_at` desc limit 5", "type": "query", "params": [], "bindings": ["no", "6056", "407", "408", "new_work_order_created", "bm_has_approved_and_evaluated_wo", "wo_completed_wo", "bm_work_order_rejected", "bm_has_did_not_agreed_on_workorder", "bm_has_reopend_wo", "wo_started_wo", "sent_to_project_owner", "work_order", "new_chat_message", "2025-06-28 16:50:26", "2025-07-28 16:50:26", "report", "21", "2025-06-28 16:50:26", "2025-07-28 16:50:26", "contracts", "2025-06-28 16:50:26", "2025-07-28 16:50:26", "variation_order", "2025-06-28 16:50:26", "2025-07-28 16:50:26", "advance_contracts", "2025-06-28 16:50:26", "2025-07-28 16:50:26", "complaints", "2025-06-28 16:50:26", "2025-07-28 16:50:26"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Traits\\NotificationTrait.php", "line": 286}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 217}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 30}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 1.13278, "duration_str": "1.13s", "stmt_id": "\\app\\Http\\Traits\\NotificationTrait.php:286", "connection": "osool_test_db", "start_percent": 1.885, "width_percent": 54.756}, {"sql": "select `notifications`.*, `work_orders`.`work_order_id`, `work_orders`.`work_order_type`, `work_orders`.`assigned_to`, `work_orders`.`supervisor_id` from `notifications` left join `work_orders` on `work_orders`.`id` = `notifications`.`section_id` left join `user_assets_mapping` on `user_assets_mapping`.`contract_id` = `work_orders`.`contract_id` where (`notifications`.`is_timeline` = 'no' or `notifications`.`is_timeline` is null) and (NOT find_in_set(21, notifications.user_id)) and (`notifications`.`building_ids` in (6056) and `user_assets_mapping`.`contract_id` in (407, 408) and `notifications`.`notification_sub_type` in ('new_work_order_created', 'bm_has_approved_and_evaluated_wo', 'wo_completed_wo', 'bm_work_order_rejected', 'bm_has_did_not_agreed_on_workorder', 'bm_has_reopend_wo', 'wo_started_wo', 'sent_to_project_owner') and `notifications`.`section_type` = 'work_order' and `notifications`.`section_type` != 'new_chat_message' and `notifications`.`created_at` between '2025-06-28 16:50:27' and '2025-07-28 16:50:26') or (`notifications`.`section_type` = 'report' and `notifications`.`user_id` = 21 and `notifications`.`created_at` between '2025-06-28 16:50:27' and '2025-07-28 16:50:26') or (`notifications`.`section_type` = 'contracts' and FIND_IN_SET(21, notifications.user_id) and `notifications`.`created_at` between '2025-06-28 16:50:27' and '2025-07-28 16:50:26') or (`notifications`.`section_type` = 'variation_order' and FIND_IN_SET( 21, notifications.user_id) and `notifications`.`created_at` between '2025-06-28 16:50:27' and '2025-07-28 16:50:26') or (`notifications`.`section_type` = 'advance_contracts' and FIND_IN_SET(21, notifications.user_id) and `notifications`.`created_at` between '2025-06-28 16:50:27' and '2025-07-28 16:50:26') or (`notifications`.`section_type` = 'complaints' and FIND_IN_SET(21, notifications.user_id) and `notifications`.`created_at` between '2025-06-28 16:50:27' and '2025-07-28 16:50:26') group by `notifications`.`id` order by `notifications`.`created_at` desc", "type": "query", "params": [], "bindings": ["no", "6056", "407", "408", "new_work_order_created", "bm_has_approved_and_evaluated_wo", "wo_completed_wo", "bm_work_order_rejected", "bm_has_did_not_agreed_on_workorder", "bm_has_reopend_wo", "wo_started_wo", "sent_to_project_owner", "work_order", "new_chat_message", "2025-06-28 16:50:27", "2025-07-28 16:50:26", "report", "21", "2025-06-28 16:50:27", "2025-07-28 16:50:26", "contracts", "2025-06-28 16:50:27", "2025-07-28 16:50:26", "variation_order", "2025-06-28 16:50:27", "2025-07-28 16:50:26", "advance_contracts", "2025-06-28 16:50:27", "2025-07-28 16:50:26", "complaints", "2025-06-28 16:50:27", "2025-07-28 16:50:26"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Traits\\NotificationTrait.php", "line": 276}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\NotificationTrait.php", "line": 297}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 227}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 31}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.89628, "duration_str": "896ms", "stmt_id": "\\app\\Http\\Traits\\NotificationTrait.php:276", "connection": "osool_test_db", "start_percent": 56.641, "width_percent": 43.324}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 191 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["191"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Providers\\AsideViewComposerServiceProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Providers\\AsideViewComposerServiceProvider.php:59", "connection": "osool_test_db", "start_percent": 99.965, "width_percent": 0.035}]}, "models": {"data": {"App\\Models\\ProjectsDetails": 1, "App\\Models\\Notification": 102, "App\\Models\\Configuration": 2, "App\\Models\\User": 1}, "count": 106}, "livewire": {"data": {"notifications.new-notifications-list-top-nav #ZO7PpZ4v8tMxs2EH8bKD": "array:5 [\n  \"data\" => array:11 [\n    \"user\" => App\\Models\\User {#3269\n      #connection: \"mysql\"\n      #table: \"users\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:78 [\n        \"id\" => 21\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$JCvJmAzGnnOeoh9po8kYju4db9GhmC.s6.ue6cmFhafhv6MgdUINu\"\n        \"name\" => \"Super Admin\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => \"123456789\"\n        \"profile_img\" => \"1641715388.png\"\n        \"emp_id\" => \"1234112\"\n        \"profession_id\" => null\n        \"emp_dept\" => \"Management\"\n        \"building_ids\" => null\n        \"contract_ids\" => \"\"\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => \"\"\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => \"1,2,3,4\"\n        \"role_cities\" => \"1,2,3,4\"\n        \"asset_categories\" => \"1,2,3,4\"\n        \"keeper_warehouses\" => null\n        \"properties\" => \"1\"\n        \"contracts\" => \"1\"\n        \"beneficiary\" => \"1\"\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"super_admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 0\n        \"project_id\" => 191\n        \"project_user_id\" => 6942\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2022-03-03 13:52:49\"\n        \"modified_at\" => \"2025-07-28 16:50:21\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"memo\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNzEwNTU2LCJleHAiOjE3NTM3MTQxNTYsIm5iZiI6MTc1MzcxMDU1NiwianRpIjoiS1RSR3dtTXd5cGszNDhzTCIsInN1YiI6IjEiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.F-HytyuBuf52RxpgRuHKyq9INjqBN5U6c4lL2F7q6fI\"\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #original: array:78 [\n        \"id\" => 21\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$JCvJmAzGnnOeoh9po8kYju4db9GhmC.s6.ue6cmFhafhv6MgdUINu\"\n        \"name\" => \"Super Admin\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => \"123456789\"\n        \"profile_img\" => \"1641715388.png\"\n        \"emp_id\" => \"1234112\"\n        \"profession_id\" => null\n        \"emp_dept\" => \"Management\"\n        \"building_ids\" => null\n        \"contract_ids\" => \"\"\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => \"\"\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => \"1,2,3,4\"\n        \"role_cities\" => \"1,2,3,4\"\n        \"asset_categories\" => \"1,2,3,4\"\n        \"keeper_warehouses\" => null\n        \"properties\" => \"1\"\n        \"contracts\" => \"1\"\n        \"beneficiary\" => \"1\"\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"super_admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 0\n        \"project_id\" => 191\n        \"project_user_id\" => 6942\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2022-03-03 13:52:49\"\n        \"modified_at\" => \"2025-07-28 16:50:21\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"memo\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNzEwNTU2LCJleHAiOjE3NTM3MTQxNTYsIm5iZiI6MTc1MzcxMDU1NiwianRpIjoiS1RSR3dtTXd5cGszNDhzTCIsInN1YiI6IjEiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.F-HytyuBuf52RxpgRuHKyq9INjqBN5U6c4lL2F7q6fI\"\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #changes: []\n      #casts: array:2 [\n        \"email_verified_at\" => \"datetime\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:1 [\n        \"projectDetails\" => App\\Models\\ProjectsDetails {#4094\n          #connection: \"mysql\"\n          #table: \"projects_details\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:23 [\n            \"id\" => 191\n            \"user_id\" => 7034\n            \"project_name\" => \"Testing\"\n            \"project_name_ar\" => \"Testing23\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => \"20250427115740146654.jpg\"\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2025-02-18 17:58:25\"\n            \"updated_at\" => \"2025-07-28 15:47:26\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 0\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 0\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => null\n            \"contract_end_date\" => null\n            \"share_post\" => 1\n            \"deleted_at\" => null\n            \"crm_workspace_slug\" => \"testing23\"\n          ]\n          #original: array:23 [\n            \"id\" => 191\n            \"user_id\" => 7034\n            \"project_name\" => \"Testing\"\n            \"project_name_ar\" => \"Testing23\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => \"20250427115740146654.jpg\"\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2025-02-18 17:58:25\"\n            \"updated_at\" => \"2025-07-28 15:47:26\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 0\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 0\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => null\n            \"contract_end_date\" => null\n            \"share_post\" => 1\n            \"deleted_at\" => null\n            \"crm_workspace_slug\" => \"testing23\"\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:16 [\n            0 => \"user_id\"\n            1 => \"project_name\"\n            2 => \"project_name_ar\"\n            3 => \"project_image\"\n            4 => \"industry_type\"\n            5 => \"created_by\"\n            6 => \"is_deleted\"\n            7 => \"use_erp_module\"\n            8 => \"use_tenant_module\"\n            9 => \"tenant_status\"\n            10 => \"use_beneficiary_module\"\n            11 => \"benificiary_status\"\n            12 => \"community_status\"\n            13 => \"contract_status\"\n            14 => \"share_post\"\n            15 => \"use_crm_module\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n          #excludedAttributes: []\n          +auditEvent: null\n          +auditCustomOld: null\n          +auditCustomNew: null\n          +isCustomEvent: false\n          +preloadedResolverData: []\n        }\n      ]\n      #touches: []\n      +timestamps: true\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: array:62 [\n        0 => \"allow_akaunting\"\n        1 => \"email\"\n        2 => \"password\"\n        3 => \"name\"\n        4 => \"first_name\"\n        5 => \"last_name\"\n        6 => \"apartment\"\n        7 => \"unit_receival_date\"\n        8 => \"later_booking_alert\"\n        9 => \"phone\"\n        10 => \"profile_img\"\n        11 => \"address\"\n        12 => \"country_id\"\n        13 => \"city_id\"\n        14 => \"role_regions\"\n        15 => \"role_cities\"\n        16 => \"asset_categories\"\n        17 => \"properties\"\n        18 => \"contracts\"\n        19 => \"beneficiary\"\n        20 => \"service_provider\"\n        21 => \"user_type\"\n        22 => \"project_id\"\n        23 => \"project_user_id\"\n        24 => \"created_by\"\n        25 => \"status\"\n        26 => \"user_privileges\"\n        27 => \"approved_max_amount\"\n        28 => \"emp_id\"\n        29 => \"profession_id\"\n        30 => \"emp_dept\"\n        31 => \"building_ids\"\n        32 => \"contract_ids\"\n        33 => \"supervisor_id\"\n        34 => \"sp_admin_id\"\n        35 => \"langForSms\"\n        36 => \"deleted_at\"\n        37 => \"otp\"\n        38 => \"temp_password\"\n        39 => \"otp_for_password\"\n        40 => \"otp_for_password_verified\"\n        41 => \"temp_phone_number\"\n        42 => \"favorite_language\"\n        43 => \"is_subcontractors_worker\"\n        44 => \"keeper_warehouses\"\n        45 => \"save_later_date\"\n        46 => \"first_login\"\n        47 => \"is_unit_link\"\n        48 => \"akaunting_vendor_id\"\n        49 => \"akaunting_customer_id\"\n        50 => \"crm_api_token\"\n        51 => \"workspace_slug\"\n        52 => \"is_bma_area_manager\"\n        53 => \"assigned_workers\"\n        54 => \"sleep_mode\"\n        55 => \"offline_mode\"\n        56 => \"attendance_mandatory\"\n        57 => \"admin_level\"\n        58 => \"role\"\n        59 => \"attendance_target\"\n        60 => \"salary\"\n        61 => \"show_extra_info\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #rememberTokenName: \"remember_token\"\n      #accessToken: null\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n    }\n    \"perPage\" => 5\n    \"assignedAsset\" => []\n    \"contractsIds\" => array:2 [\n      0 => 407\n      1 => 408\n    ]\n    \"accessBuildingsIds\" => array:1 [\n      0 => 6056\n    ]\n    \"currentDate\" => \"2025-07-28\"\n    \"currentDateTime\" => \"2025-07-28 16:50:26\"\n    \"readyToLoad\" => true\n    \"configOciLink\" => App\\Models\\Configuration {#4249\n      #connection: \"mysql\"\n      #table: \"configurations\"\n      #primaryKey: \"id_configuration\"\n      #keyType: \"int\"\n      +incrementing: false\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id_configuration\" => 3\n        \"name\" => \"OciLink\"\n        \"code\" => 3\n        \"value\" => \"https://axpem4hcpiq4.compat.objectstorage.me-jeddah-1.oraclecloud.com\"\n        \"description\" => \"The OCI storage link\"\n        \"active\" => 1\n        \"platform\" => \"1\"\n      ]\n      #original: array:7 [\n        \"id_configuration\" => 3\n        \"name\" => \"OciLink\"\n        \"code\" => 3\n        \"value\" => \"https://axpem4hcpiq4.compat.objectstorage.me-jeddah-1.oraclecloud.com\"\n        \"description\" => \"The OCI storage link\"\n        \"active\" => 1\n        \"platform\" => \"1\"\n      ]\n      #changes: []\n      #casts: array:3 [\n        \"platform\" => \"App\\Enums\\Platform\"\n        \"active\" => \"App\\Enums\\Status\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:13 [\n        0 => \"id_configuration\"\n        1 => \"platform\"\n        2 => \"name\"\n        3 => \"code\"\n        4 => \"value\"\n        5 => \"description\"\n        6 => \"active\"\n        7 => \"created_by\"\n        8 => \"deleted_by\"\n        9 => \"updated_by\"\n        10 => \"deleted_at\"\n        11 => \"created_at\"\n        12 => \"updated_at\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"ociLink\" => \"https://axpem4hcpiq4.compat.objectstorage.me-jeddah-1.oraclecloud.com\"\n    \"selectedLanguage\" => \"en\"\n  ]\n  \"name\" => \"notifications.new-notifications-list-top-nav\"\n  \"view\" => \"livewire.notifications.new-notifications-list-top-nav\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav\"\n  \"id\" => \"ZO7PpZ4v8tMxs2EH8bKD\"\n]"}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"locale": "en", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/dashboards/admin\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "21", "plain_user_password": "Tarqeem21", "entered_project_id": "191", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/livewire/message/notifications.new-notifications-list-top-nav", "status_code": "<pre class=sf-dump id=sf-dump-1676022244 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1676022244\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-926221389 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-926221389\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1365349853 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">ZO7PpZ4v8tMxs2EH8bKD</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"44 characters\">notifications.new-notifications-list-top-nav</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"16 characters\">dashboards/admin</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">47a312c0</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>perPage</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>assignedAsset</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>contractsIds</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>accessBuildingsIds</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>currentDate</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>currentDateTime</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>readyToLoad</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>configOciLink</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>ociLink</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>selectedLanguage</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">e359c15bac324901b16f3c402a149f285db4f85f7e9edf3889cbe7d1a999a4e8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">25lr</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"8 characters\">loadData</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1365349853\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1115696377 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">633</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://osool-b2g.test/dashboards/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IlF4azY1cXU5Y0dwSnA4MUhFWk4wSEE9PSIsInZhbHVlIjoiYytTa3NWOWFKbDJMUW9sUkRxdXpBMkNoWHdld003MjNtRFp0QzJPd0lRdFFqM0dORnpsU0x6MUtTb2R1d1p5N3FFcUdGU1JBRHdpVnFZNzRIWHE4SmV0YTZUc3JvaEFTSGJHRVBKYUloVTJvdVU2M0JlK3FmM0sxZ2tBenVBT24iLCJtYWMiOiIxMjMzOGIxY2E3NjRkNTUyNmZmYjcwNDhhYzMwZGNjMWUzN2RjODVhNmRlNjI1OTk0OTY2NTA3Mzk5ZDYwYjg0IiwidGFnIjoiIn0%3D; osool_session=XzOD0ToWvNGBHiqr85ElEoWZirExjOyR4t8uXK8F</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1115696377\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1962069030 data-indent-pad=\"  \"><span class=sf-dump-note>array:43</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">633</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://osool-b2g.test/dashboards/admin</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IlF4azY1cXU5Y0dwSnA4MUhFWk4wSEE9PSIsInZhbHVlIjoiYytTa3NWOWFKbDJMUW9sUkRxdXpBMkNoWHdld003MjNtRFp0QzJPd0lRdFFqM0dORnpsU0x6MUtTb2R1d1p5N3FFcUdGU1JBRHdpVnFZNzRIWHE4SmV0YTZUc3JvaEFTSGJHRVBKYUloVTJvdVU2M0JlK3FmM0sxZ2tBenVBT24iLCJtYWMiOiIxMjMzOGIxY2E3NjRkNTUyNmZmYjcwNDhhYzMwZGNjMWUzN2RjODVhNmRlNjI1OTk0OTY2NTA3Mzk5ZDYwYjg0IiwidGFnIjoiIn0%3D; osool_session=XzOD0ToWvNGBHiqr85ElEoWZirExjOyR4t8uXK8F</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52510</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"62 characters\">/livewire/message/notifications.new-notifications-list-top-nav</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"62 characters\">/livewire/message/notifications.new-notifications-list-top-nav</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753710625.0453</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753710625</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1962069030\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1404727844 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1404727844\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1180405561 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 13:50:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlFLMjBoS0ZyNTd4OHVFWS9KaHcxYkE9PSIsInZhbHVlIjoiWk9KQjhlYVlIcHNyaXV5VFNZTGo5aVZPbHBGNEU0MFpBYXRZTXNWOGE5QjJocDYzNVpoU1lSSXlMSE5rOWdtQjUrK1pKMVRJOSsyaDkzS3YwblJDRFo1RVlYZnB4MTVCbUw4V05vQUJiNlVudWt3Z1RPR1gzMitWSFRDZGxJdGYiLCJtYWMiOiIyN2QwMDJmY2FjOGM1MjJlMTM2NjU1OWJiYTc2MTU1OTI4YzI2YTYzYzU3YzJkNDRmNGZiYTMwNDc2OTczMzc4IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:50:28 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IlUvcEZCNVZ5S3BrQzZJcW5YL0lPekE9PSIsInZhbHVlIjoiMjdkT2lZL1BUWHB6cmxwTkVtZGg3cUFQcS82YkxmeVE2aS9zRkVjRkdzeElIUjVEYmJsZFhRVmlBMitJOTBtNnVBRW9VQ3lPaTcyTStudS8vL2p2WEtPTm5MTWFGc2ZOZFhjSkJ0ZFc2YlhBWjA0Mmlub1ZURnhPQjhaOFNjQTkiLCJtYWMiOiJhNDUzN2NiMDkyODM2YzdiZTdkNjBmYTJiY2I3NjA3MDE5Yzk5YjIwMWFlZDk5ZDkzZjAxYWJjNmQxMjZmZmRmIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:50:28 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlFLMjBoS0ZyNTd4OHVFWS9KaHcxYkE9PSIsInZhbHVlIjoiWk9KQjhlYVlIcHNyaXV5VFNZTGo5aVZPbHBGNEU0MFpBYXRZTXNWOGE5QjJocDYzNVpoU1lSSXlMSE5rOWdtQjUrK1pKMVRJOSsyaDkzS3YwblJDRFo1RVlYZnB4MTVCbUw4V05vQUJiNlVudWt3Z1RPR1gzMitWSFRDZGxJdGYiLCJtYWMiOiIyN2QwMDJmY2FjOGM1MjJlMTM2NjU1OWJiYTc2MTU1OTI4YzI2YTYzYzU3YzJkNDRmNGZiYTMwNDc2OTczMzc4IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:50:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IlUvcEZCNVZ5S3BrQzZJcW5YL0lPekE9PSIsInZhbHVlIjoiMjdkT2lZL1BUWHB6cmxwTkVtZGg3cUFQcS82YkxmeVE2aS9zRkVjRkdzeElIUjVEYmJsZFhRVmlBMitJOTBtNnVBRW9VQ3lPaTcyTStudS8vL2p2WEtPTm5MTWFGc2ZOZFhjSkJ0ZFc2YlhBWjA0Mmlub1ZURnhPQjhaOFNjQTkiLCJtYWMiOiJhNDUzN2NiMDkyODM2YzdiZTdkNjBmYTJiY2I3NjA3MDE5Yzk5YjIwMWFlZDk5ZDkzZjAxYWJjNmQxMjZmZmRmIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:50:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1180405561\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1309751779 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://osool-b2g.test/dashboards/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>21</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Tarqeem21</span>\"\n  \"<span class=sf-dump-key>entered_project_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">191</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1309751779\", {\"maxDepth\":0})</script>\n"}}