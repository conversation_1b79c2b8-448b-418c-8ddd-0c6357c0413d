{"__meta": {"id": "X5f135590748f65259c3591fc57c30981", "datetime": "2025-07-28 16:46:54", "utime": **********.506986, "method": "GET", "uri": "/workspace/projects", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 15, "messages": [{"message": "[16:46:50] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.958158, "xdebug_link": null, "collector": "log"}, {"message": "[16:46:51] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2418", "message_html": null, "is_string": false, "label": "warning", "time": 1753710411.014451, "xdebug_link": null, "collector": "log"}, {"message": "[16:46:51] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2478", "message_html": null, "is_string": false, "label": "warning", "time": 1753710411.014726, "xdebug_link": null, "collector": "log"}, {"message": "[16:46:51] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3619", "message_html": null, "is_string": false, "label": "warning", "time": 1753710411.015451, "xdebug_link": null, "collector": "log"}, {"message": "[16:46:52] LOG.debug: Checking existence of file: uploads/profile_images/1641715388.png", "message_html": null, "is_string": false, "label": "debug", "time": 1753710412.339615, "xdebug_link": null, "collector": "log"}, {"message": "[16:46:54] LOG.debug: File exists: false", "message_html": null, "is_string": false, "label": "debug", "time": **********.115672, "xdebug_link": null, "collector": "log"}, {"message": "[16:46:54] LOG.debug: Checking existence of file: uploads/profile_images/1641715388.png", "message_html": null, "is_string": false, "label": "debug", "time": **********.116244, "xdebug_link": null, "collector": "log"}, {"message": "[16:46:54] LOG.debug: File exists: false", "message_html": null, "is_string": false, "label": "debug", "time": **********.349914, "xdebug_link": null, "collector": "log"}, {"message": "[16:46:54] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.370156, "xdebug_link": null, "collector": "log"}, {"message": "[16:46:54] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.370436, "xdebug_link": null, "collector": "log"}, {"message": "[16:46:54] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.370496, "xdebug_link": null, "collector": "log"}, {"message": "[16:46:54] LOG.warning: Optional parameter $serviceProviderId declared before required parameter $search is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\UserTrait.php on line 328", "message_html": null, "is_string": false, "label": "warning", "time": **********.373401, "xdebug_link": null, "collector": "log"}, {"message": "[16:46:54] LOG.warning: Optional parameter $filters declared before required parameter $userServiceProvider is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\WorkOrdersTrait.php on line 3418", "message_html": null, "is_string": false, "label": "warning", "time": **********.38445, "xdebug_link": null, "collector": "log"}, {"message": "[16:46:54] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 637", "message_html": null, "is_string": false, "label": "warning", "time": **********.402048, "xdebug_link": null, "collector": "log"}, {"message": "[16:46:54] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\f8d18d6f63e9bf36e98d3bb1b82c9e7d86b4c71c.php on line 3", "message_html": null, "is_string": false, "label": "warning", "time": **********.456725, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.509403, "end": **********.507037, "duration": 3.997633934020996, "duration_str": "4s", "measures": [{"label": "Booting", "start": **********.509403, "relative_start": 0, "end": **********.934801, "relative_end": **********.934801, "duration": 0.4253981113433838, "duration_str": "425ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.934812, "relative_start": 0.42540907859802246, "end": **********.507039, "relative_end": 2.1457672119140625e-06, "duration": 3.5722270011901855, "duration_str": "3.57s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 58865576, "peak_usage_str": "56MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 8, "templates": [{"name": "applications.admin.workspace.projects.list (\\resources\\views\\applications\\admin\\workspace\\projects\\list.blade.php)", "param_count": 14, "params": ["data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.app (\\resources\\views\\layouts\\app.blade.php)", "param_count": 17, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.partials._styles (\\resources\\views\\layouts\\partials\\_styles.blade.php)", "param_count": 17, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.partials._header (\\resources\\views\\layouts\\partials\\_header.blade.php)", "param_count": 17, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.partials._top_menu (\\resources\\views\\layouts\\partials\\_top_menu.blade.php)", "param_count": 17, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.menu.aside-nav-list (\\resources\\views\\livewire\\menu\\aside-nav-list.blade.php)", "param_count": 27, "params": ["userPrivilegesAside", "user", "hasViewPrivilege", "errors", "_instance", "has<PERSON>dmin", "projectId", "project", "workOrderMenuItemColor", "flagWorkorderSidebarMenu", "userPrivileges", "closedWorkOrderCount", "maintenanceRequestCount", "vendorRegistrationApplicationRequests", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.partials._footer (\\resources\\views\\layouts\\partials\\_footer.blade.php)", "param_count": 20, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html", "url", "segments"], "type": "blade"}, {"name": "layouts.partials._scripts (\\resources\\views\\layouts\\partials\\_scripts.blade.php)", "param_count": 20, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html", "url", "segments"], "type": "blade"}]}, "route": {"uri": "GET workspace/projects", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController@project", "as": "workspace.project", "namespace": "App\\Http\\Controllers\\Admin\\Workspace", "prefix": "/workspace", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php&line=333\">\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:333-400</a>"}, "queries": {"nb_statements": 157, "nb_failed_statements": 0, "accumulated_duration": 1.09311, "accumulated_duration_str": "1.09s", "statements": [{"sql": "select * from `users` where `id` = 21 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00382, "duration_str": "3.82ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 0.349}, {"sql": "select * from `projects_details` where `is_deleted` = '0' and `deleted_at` is null order by `id` desc", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 378}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00248, "duration_str": "2.48ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:378", "connection": "osool_test_db", "start_percent": 0.349, "width_percent": 0.227}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 205 limit 1", "type": "query", "params": [], "bindings": ["admin", "205"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.02893, "duration_str": "28.93ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 0.576, "width_percent": 2.647}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 204 limit 1", "type": "query", "params": [], "bindings": ["admin", "204"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01226, "duration_str": "12.26ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 3.223, "width_percent": 1.122}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 203 limit 1", "type": "query", "params": [], "bindings": ["admin", "203"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.013, "duration_str": "13ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 4.344, "width_percent": 1.189}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 202 limit 1", "type": "query", "params": [], "bindings": ["admin", "202"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01177, "duration_str": "11.77ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 5.534, "width_percent": 1.077}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 201 limit 1", "type": "query", "params": [], "bindings": ["admin", "201"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.013550000000000001, "duration_str": "13.55ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 6.61, "width_percent": 1.24}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 200 limit 1", "type": "query", "params": [], "bindings": ["admin", "200"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.011439999999999999, "duration_str": "11.44ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 7.85, "width_percent": 1.047}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 199 limit 1", "type": "query", "params": [], "bindings": ["admin", "199"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.011529999999999999, "duration_str": "11.53ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 8.897, "width_percent": 1.055}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 198 limit 1", "type": "query", "params": [], "bindings": ["admin", "198"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.012320000000000001, "duration_str": "12.32ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 9.951, "width_percent": 1.127}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 197 limit 1", "type": "query", "params": [], "bindings": ["admin", "197"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01267, "duration_str": "12.67ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 11.078, "width_percent": 1.159}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 196 limit 1", "type": "query", "params": [], "bindings": ["admin", "196"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01271, "duration_str": "12.71ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 12.238, "width_percent": 1.163}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 195 limit 1", "type": "query", "params": [], "bindings": ["admin", "195"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.012240000000000001, "duration_str": "12.24ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 13.4, "width_percent": 1.12}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 194 limit 1", "type": "query", "params": [], "bindings": ["admin", "194"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01185, "duration_str": "11.85ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 14.52, "width_percent": 1.084}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 193 limit 1", "type": "query", "params": [], "bindings": ["admin", "193"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01166, "duration_str": "11.66ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 15.604, "width_percent": 1.067}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 192 limit 1", "type": "query", "params": [], "bindings": ["admin", "192"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01268, "duration_str": "12.68ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 16.671, "width_percent": 1.16}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 191 limit 1", "type": "query", "params": [], "bindings": ["admin", "191"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.011789999999999998, "duration_str": "11.79ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 17.831, "width_percent": 1.079}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 190 limit 1", "type": "query", "params": [], "bindings": ["admin", "190"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01213, "duration_str": "12.13ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 18.909, "width_percent": 1.11}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 189 limit 1", "type": "query", "params": [], "bindings": ["admin", "189"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01149, "duration_str": "11.49ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 20.019, "width_percent": 1.051}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 188 limit 1", "type": "query", "params": [], "bindings": ["admin", "188"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.011630000000000001, "duration_str": "11.63ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 21.07, "width_percent": 1.064}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 187 limit 1", "type": "query", "params": [], "bindings": ["admin", "187"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0125, "duration_str": "12.5ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 22.134, "width_percent": 1.144}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 186 limit 1", "type": "query", "params": [], "bindings": ["admin", "186"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0119, "duration_str": "11.9ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 23.278, "width_percent": 1.089}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 185 limit 1", "type": "query", "params": [], "bindings": ["admin", "185"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0114, "duration_str": "11.4ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 24.366, "width_percent": 1.043}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 184 limit 1", "type": "query", "params": [], "bindings": ["admin", "184"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01201, "duration_str": "12.01ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 25.409, "width_percent": 1.099}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 183 limit 1", "type": "query", "params": [], "bindings": ["admin", "183"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.012060000000000001, "duration_str": "12.06ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 26.508, "width_percent": 1.103}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 182 limit 1", "type": "query", "params": [], "bindings": ["admin", "182"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01185, "duration_str": "11.85ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 27.611, "width_percent": 1.084}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 181 limit 1", "type": "query", "params": [], "bindings": ["admin", "181"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01244, "duration_str": "12.44ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 28.695, "width_percent": 1.138}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 180 limit 1", "type": "query", "params": [], "bindings": ["admin", "180"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.011609999999999999, "duration_str": "11.61ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 29.833, "width_percent": 1.062}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 179 limit 1", "type": "query", "params": [], "bindings": ["admin", "179"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.012039999999999999, "duration_str": "12.04ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 30.895, "width_percent": 1.101}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 178 limit 1", "type": "query", "params": [], "bindings": ["admin", "178"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01122, "duration_str": "11.22ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 31.997, "width_percent": 1.026}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 177 limit 1", "type": "query", "params": [], "bindings": ["admin", "177"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01185, "duration_str": "11.85ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 33.023, "width_percent": 1.084}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 176 limit 1", "type": "query", "params": [], "bindings": ["admin", "176"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01233, "duration_str": "12.33ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 34.107, "width_percent": 1.128}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 175 limit 1", "type": "query", "params": [], "bindings": ["admin", "175"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01074, "duration_str": "10.74ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 35.235, "width_percent": 0.983}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 174 limit 1", "type": "query", "params": [], "bindings": ["admin", "174"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01126, "duration_str": "11.26ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 36.218, "width_percent": 1.03}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 173 limit 1", "type": "query", "params": [], "bindings": ["admin", "173"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01117, "duration_str": "11.17ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 37.248, "width_percent": 1.022}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 172 limit 1", "type": "query", "params": [], "bindings": ["admin", "172"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.011640000000000001, "duration_str": "11.64ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 38.27, "width_percent": 1.065}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 171 limit 1", "type": "query", "params": [], "bindings": ["admin", "171"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.011980000000000001, "duration_str": "11.98ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 39.335, "width_percent": 1.096}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 170 limit 1", "type": "query", "params": [], "bindings": ["admin", "170"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01125, "duration_str": "11.25ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 40.431, "width_percent": 1.029}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 169 limit 1", "type": "query", "params": [], "bindings": ["admin", "169"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01076, "duration_str": "10.76ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 41.46, "width_percent": 0.984}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 168 limit 1", "type": "query", "params": [], "bindings": ["admin", "168"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01066, "duration_str": "10.66ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 42.444, "width_percent": 0.975}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 167 limit 1", "type": "query", "params": [], "bindings": ["admin", "167"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01168, "duration_str": "11.68ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 43.419, "width_percent": 1.069}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 166 limit 1", "type": "query", "params": [], "bindings": ["admin", "166"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.013009999999999999, "duration_str": "13.01ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 44.488, "width_percent": 1.19}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 165 limit 1", "type": "query", "params": [], "bindings": ["admin", "165"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01339, "duration_str": "13.39ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 45.678, "width_percent": 1.225}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 164 limit 1", "type": "query", "params": [], "bindings": ["admin", "164"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01192, "duration_str": "11.92ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 46.903, "width_percent": 1.09}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 163 limit 1", "type": "query", "params": [], "bindings": ["admin", "163"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01024, "duration_str": "10.24ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 47.993, "width_percent": 0.937}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 162 limit 1", "type": "query", "params": [], "bindings": ["admin", "162"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01035, "duration_str": "10.35ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 48.93, "width_percent": 0.947}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 161 limit 1", "type": "query", "params": [], "bindings": ["admin", "161"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.011619999999999998, "duration_str": "11.62ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 49.877, "width_percent": 1.063}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 160 limit 1", "type": "query", "params": [], "bindings": ["admin", "160"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01158, "duration_str": "11.58ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 50.94, "width_percent": 1.059}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 159 limit 1", "type": "query", "params": [], "bindings": ["admin", "159"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.010320000000000001, "duration_str": "10.32ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 51.999, "width_percent": 0.944}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 158 limit 1", "type": "query", "params": [], "bindings": ["admin", "158"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00915, "duration_str": "9.15ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 52.943, "width_percent": 0.837}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 157 limit 1", "type": "query", "params": [], "bindings": ["admin", "157"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.010369999999999999, "duration_str": "10.37ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 53.78, "width_percent": 0.949}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 156 limit 1", "type": "query", "params": [], "bindings": ["admin", "156"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0099, "duration_str": "9.9ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 54.729, "width_percent": 0.906}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 155 limit 1", "type": "query", "params": [], "bindings": ["admin", "155"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01086, "duration_str": "10.86ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 55.635, "width_percent": 0.993}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 154 limit 1", "type": "query", "params": [], "bindings": ["admin", "154"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00973, "duration_str": "9.73ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 56.628, "width_percent": 0.89}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 153 limit 1", "type": "query", "params": [], "bindings": ["admin", "153"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00941, "duration_str": "9.41ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 57.518, "width_percent": 0.861}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 152 limit 1", "type": "query", "params": [], "bindings": ["admin", "152"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.009699999999999999, "duration_str": "9.7ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 58.379, "width_percent": 0.887}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 151 limit 1", "type": "query", "params": [], "bindings": ["admin", "151"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.009529999999999999, "duration_str": "9.53ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 59.267, "width_percent": 0.872}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 150 limit 1", "type": "query", "params": [], "bindings": ["admin", "150"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.009300000000000001, "duration_str": "9.3ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 60.139, "width_percent": 0.851}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 149 limit 1", "type": "query", "params": [], "bindings": ["admin", "149"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.010490000000000001, "duration_str": "10.49ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 60.989, "width_percent": 0.96}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 148 limit 1", "type": "query", "params": [], "bindings": ["admin", "148"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.009949999999999999, "duration_str": "9.95ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 61.949, "width_percent": 0.91}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 147 limit 1", "type": "query", "params": [], "bindings": ["admin", "147"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0089, "duration_str": "8.9ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 62.859, "width_percent": 0.814}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 146 limit 1", "type": "query", "params": [], "bindings": ["admin", "146"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00887, "duration_str": "8.87ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 63.673, "width_percent": 0.811}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 145 limit 1", "type": "query", "params": [], "bindings": ["admin", "145"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01233, "duration_str": "12.33ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 64.485, "width_percent": 1.128}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 144 limit 1", "type": "query", "params": [], "bindings": ["admin", "144"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00855, "duration_str": "8.55ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 65.613, "width_percent": 0.782}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 143 limit 1", "type": "query", "params": [], "bindings": ["admin", "143"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.009460000000000001, "duration_str": "9.46ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 66.395, "width_percent": 0.865}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 142 limit 1", "type": "query", "params": [], "bindings": ["admin", "142"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00894, "duration_str": "8.94ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 67.26, "width_percent": 0.818}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 141 limit 1", "type": "query", "params": [], "bindings": ["admin", "141"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00848, "duration_str": "8.48ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 68.078, "width_percent": 0.776}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 140 limit 1", "type": "query", "params": [], "bindings": ["admin", "140"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0086, "duration_str": "8.6ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 68.854, "width_percent": 0.787}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 138 limit 1", "type": "query", "params": [], "bindings": ["admin", "138"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00839, "duration_str": "8.39ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 69.641, "width_percent": 0.768}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 137 limit 1", "type": "query", "params": [], "bindings": ["admin", "137"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.008369999999999999, "duration_str": "8.37ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 70.408, "width_percent": 0.766}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 136 limit 1", "type": "query", "params": [], "bindings": ["admin", "136"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01045, "duration_str": "10.45ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 71.174, "width_percent": 0.956}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 135 limit 1", "type": "query", "params": [], "bindings": ["admin", "135"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0081, "duration_str": "8.1ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 72.13, "width_percent": 0.741}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 134 limit 1", "type": "query", "params": [], "bindings": ["admin", "134"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00824, "duration_str": "8.24ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 72.871, "width_percent": 0.754}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 133 limit 1", "type": "query", "params": [], "bindings": ["admin", "133"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0088, "duration_str": "8.8ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 73.625, "width_percent": 0.805}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 132 limit 1", "type": "query", "params": [], "bindings": ["admin", "132"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00788, "duration_str": "7.88ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 74.43, "width_percent": 0.721}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 130 limit 1", "type": "query", "params": [], "bindings": ["admin", "130"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00836, "duration_str": "8.36ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 75.151, "width_percent": 0.765}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 129 limit 1", "type": "query", "params": [], "bindings": ["admin", "129"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00775, "duration_str": "7.75ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 75.916, "width_percent": 0.709}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 128 limit 1", "type": "query", "params": [], "bindings": ["admin", "128"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0079, "duration_str": "7.9ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 76.624, "width_percent": 0.723}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 127 limit 1", "type": "query", "params": [], "bindings": ["admin", "127"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00292, "duration_str": "2.92ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 77.347, "width_percent": 0.267}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 125 limit 1", "type": "query", "params": [], "bindings": ["admin", "125"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0032400000000000003, "duration_str": "3.24ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 77.614, "width_percent": 0.296}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 124 limit 1", "type": "query", "params": [], "bindings": ["admin", "124"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00239, "duration_str": "2.39ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 77.911, "width_percent": 0.219}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 123 limit 1", "type": "query", "params": [], "bindings": ["admin", "123"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00218, "duration_str": "2.18ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 78.129, "width_percent": 0.199}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 111 limit 1", "type": "query", "params": [], "bindings": ["admin", "111"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00239, "duration_str": "2.39ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 78.329, "width_percent": 0.219}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 89 limit 1", "type": "query", "params": [], "bindings": ["admin", "89"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0020299999999999997, "duration_str": "2.03ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 78.547, "width_percent": 0.186}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 88 limit 1", "type": "query", "params": [], "bindings": ["admin", "88"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0025099999999999996, "duration_str": "2.51ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 78.733, "width_percent": 0.23}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 86 limit 1", "type": "query", "params": [], "bindings": ["admin", "86"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00207, "duration_str": "2.07ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 78.963, "width_percent": 0.189}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 84 limit 1", "type": "query", "params": [], "bindings": ["admin", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00213, "duration_str": "2.13ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 79.152, "width_percent": 0.195}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 83 limit 1", "type": "query", "params": [], "bindings": ["admin", "83"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0020499999999999997, "duration_str": "2.05ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 79.347, "width_percent": 0.188}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 82 limit 1", "type": "query", "params": [], "bindings": ["admin", "82"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00249, "duration_str": "2.49ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 79.535, "width_percent": 0.228}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 81 limit 1", "type": "query", "params": [], "bindings": ["admin", "81"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0020800000000000003, "duration_str": "2.08ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 79.762, "width_percent": 0.19}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 80 limit 1", "type": "query", "params": [], "bindings": ["admin", "80"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00172, "duration_str": "1.72ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 79.953, "width_percent": 0.157}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 79 limit 1", "type": "query", "params": [], "bindings": ["admin", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00202, "duration_str": "2.02ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 80.11, "width_percent": 0.185}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 78 limit 1", "type": "query", "params": [], "bindings": ["admin", "78"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00176, "duration_str": "1.76ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 80.295, "width_percent": 0.161}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 77 limit 1", "type": "query", "params": [], "bindings": ["admin", "77"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00273, "duration_str": "2.73ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 80.456, "width_percent": 0.25}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 76 limit 1", "type": "query", "params": [], "bindings": ["admin", "76"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00212, "duration_str": "2.12ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 80.706, "width_percent": 0.194}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 75 limit 1", "type": "query", "params": [], "bindings": ["admin", "75"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0021000000000000003, "duration_str": "2.1ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 80.899, "width_percent": 0.192}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 74 limit 1", "type": "query", "params": [], "bindings": ["admin", "74"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00149, "duration_str": "1.49ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 81.092, "width_percent": 0.136}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 73 limit 1", "type": "query", "params": [], "bindings": ["admin", "73"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0014, "duration_str": "1.4ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 81.228, "width_percent": 0.128}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 72 limit 1", "type": "query", "params": [], "bindings": ["admin", "72"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00182, "duration_str": "1.82ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 81.356, "width_percent": 0.166}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 71 limit 1", "type": "query", "params": [], "bindings": ["admin", "71"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01167, "duration_str": "11.67ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 81.522, "width_percent": 1.068}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 70 limit 1", "type": "query", "params": [], "bindings": ["admin", "70"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0025, "duration_str": "2.5ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 82.59, "width_percent": 0.229}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 69 limit 1", "type": "query", "params": [], "bindings": ["admin", "69"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00135, "duration_str": "1.35ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 82.819, "width_percent": 0.124}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 68 limit 1", "type": "query", "params": [], "bindings": ["admin", "68"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00125, "duration_str": "1.25ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 82.942, "width_percent": 0.114}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 67 limit 1", "type": "query", "params": [], "bindings": ["admin", "67"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00235, "duration_str": "2.35ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 83.057, "width_percent": 0.215}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 66 limit 1", "type": "query", "params": [], "bindings": ["admin", "66"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01166, "duration_str": "11.66ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 83.272, "width_percent": 1.067}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 65 limit 1", "type": "query", "params": [], "bindings": ["admin", "65"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00235, "duration_str": "2.35ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 84.338, "width_percent": 0.215}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 64 limit 1", "type": "query", "params": [], "bindings": ["admin", "64"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00165, "duration_str": "1.65ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 84.553, "width_percent": 0.151}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 63 limit 1", "type": "query", "params": [], "bindings": ["admin", "63"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01247, "duration_str": "12.47ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 84.704, "width_percent": 1.141}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 62 limit 1", "type": "query", "params": [], "bindings": ["admin", "62"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00148, "duration_str": "1.48ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 85.845, "width_percent": 0.135}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 61 limit 1", "type": "query", "params": [], "bindings": ["admin", "61"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00139, "duration_str": "1.39ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 85.98, "width_percent": 0.127}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 60 limit 1", "type": "query", "params": [], "bindings": ["admin", "60"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00152, "duration_str": "1.52ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 86.108, "width_percent": 0.139}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 59 limit 1", "type": "query", "params": [], "bindings": ["admin", "59"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01109, "duration_str": "11.09ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 86.247, "width_percent": 1.015}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 58 limit 1", "type": "query", "params": [], "bindings": ["admin", "58"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01231, "duration_str": "12.31ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 87.261, "width_percent": 1.126}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 57 limit 1", "type": "query", "params": [], "bindings": ["admin", "57"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.011609999999999999, "duration_str": "11.61ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 88.387, "width_percent": 1.062}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 56 limit 1", "type": "query", "params": [], "bindings": ["admin", "56"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0014299999999999998, "duration_str": "1.43ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 89.449, "width_percent": 0.131}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 55 limit 1", "type": "query", "params": [], "bindings": ["admin", "55"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00108, "duration_str": "1.08ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 89.58, "width_percent": 0.099}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 54 limit 1", "type": "query", "params": [], "bindings": ["admin", "54"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 89.679, "width_percent": 0.097}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 53 limit 1", "type": "query", "params": [], "bindings": ["admin", "53"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0014, "duration_str": "1.4ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 89.776, "width_percent": 0.128}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 52 limit 1", "type": "query", "params": [], "bindings": ["admin", "52"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00136, "duration_str": "1.36ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 89.904, "width_percent": 0.124}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 51 limit 1", "type": "query", "params": [], "bindings": ["admin", "51"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00166, "duration_str": "1.66ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 90.028, "width_percent": 0.152}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 50 limit 1", "type": "query", "params": [], "bindings": ["admin", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00222, "duration_str": "2.22ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 90.18, "width_percent": 0.203}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 49 limit 1", "type": "query", "params": [], "bindings": ["admin", "49"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00139, "duration_str": "1.39ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 90.383, "width_percent": 0.127}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 48 limit 1", "type": "query", "params": [], "bindings": ["admin", "48"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.013210000000000001, "duration_str": "13.21ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 90.511, "width_percent": 1.208}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 47 limit 1", "type": "query", "params": [], "bindings": ["admin", "47"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01156, "duration_str": "11.56ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 91.719, "width_percent": 1.058}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 46 limit 1", "type": "query", "params": [], "bindings": ["admin", "46"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00136, "duration_str": "1.36ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 92.777, "width_percent": 0.124}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 45 limit 1", "type": "query", "params": [], "bindings": ["admin", "45"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01134, "duration_str": "11.34ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 92.901, "width_percent": 1.037}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 44 limit 1", "type": "query", "params": [], "bindings": ["admin", "44"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0016899999999999999, "duration_str": "1.69ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 93.938, "width_percent": 0.155}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 43 limit 1", "type": "query", "params": [], "bindings": ["admin", "43"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00095, "duration_str": "950μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 94.093, "width_percent": 0.087}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 42 limit 1", "type": "query", "params": [], "bindings": ["admin", "42"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00087, "duration_str": "870μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 94.18, "width_percent": 0.08}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 41 limit 1", "type": "query", "params": [], "bindings": ["admin", "41"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00103, "duration_str": "1.03ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 94.259, "width_percent": 0.094}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 40 limit 1", "type": "query", "params": [], "bindings": ["admin", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00942, "duration_str": "9.42ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 94.354, "width_percent": 0.862}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 39 limit 1", "type": "query", "params": [], "bindings": ["admin", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00146, "duration_str": "1.46ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 95.215, "width_percent": 0.134}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 38 limit 1", "type": "query", "params": [], "bindings": ["admin", "38"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00134, "duration_str": "1.34ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 95.349, "width_percent": 0.123}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 37 limit 1", "type": "query", "params": [], "bindings": ["admin", "37"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00115, "duration_str": "1.15ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 95.472, "width_percent": 0.105}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 36 limit 1", "type": "query", "params": [], "bindings": ["admin", "36"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00136, "duration_str": "1.36ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 95.577, "width_percent": 0.124}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 35 limit 1", "type": "query", "params": [], "bindings": ["admin", "35"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0009599999999999999, "duration_str": "960μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 95.701, "width_percent": 0.088}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 34 limit 1", "type": "query", "params": [], "bindings": ["admin", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0013700000000000001, "duration_str": "1.37ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 95.789, "width_percent": 0.125}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 33 limit 1", "type": "query", "params": [], "bindings": ["admin", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0010500000000000002, "duration_str": "1.05ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 95.914, "width_percent": 0.096}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 32 limit 1", "type": "query", "params": [], "bindings": ["admin", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00121, "duration_str": "1.21ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 96.01, "width_percent": 0.111}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 31 limit 1", "type": "query", "params": [], "bindings": ["admin", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00115, "duration_str": "1.15ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 96.121, "width_percent": 0.105}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 30 limit 1", "type": "query", "params": [], "bindings": ["admin", "30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 96.226, "width_percent": 0.091}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 29 limit 1", "type": "query", "params": [], "bindings": ["admin", "29"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01517, "duration_str": "15.17ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 96.317, "width_percent": 1.388}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 28 limit 1", "type": "query", "params": [], "bindings": ["admin", "28"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 97.705, "width_percent": 0.083}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 27 limit 1", "type": "query", "params": [], "bindings": ["admin", "27"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00134, "duration_str": "1.34ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 97.788, "width_percent": 0.123}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 26 limit 1", "type": "query", "params": [], "bindings": ["admin", "26"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00095, "duration_str": "950μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 97.911, "width_percent": 0.087}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 25 limit 1", "type": "query", "params": [], "bindings": ["admin", "25"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0010400000000000001, "duration_str": "1.04ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 97.997, "width_percent": 0.095}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 24 limit 1", "type": "query", "params": [], "bindings": ["admin", "24"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00125, "duration_str": "1.25ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 98.093, "width_percent": 0.114}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 23 limit 1", "type": "query", "params": [], "bindings": ["admin", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 387}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 98.207, "width_percent": 0.066}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 0 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Providers\\AsideViewComposerServiceProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "\\app\\Providers\\AsideViewComposerServiceProvider.php:59", "connection": "osool_test_db", "start_percent": 98.273, "width_percent": 0.085}, {"sql": "select `name`, `name_ar` from `user_type` where `slug` = 'super_admin' limit 1", "type": "query", "params": [], "bindings": ["super_admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1929}, {"index": 14, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 161}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00404, "duration_str": "4.04ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1929", "connection": "osool_test_db", "start_percent": 98.358, "width_percent": 0.37}, {"sql": "select `id`, `project_image`, `use_beneficiary_module`, `use_tenant_module`, `benificiary_status`, `tenant_status`, `project_name`, `project_name_ar`, `use_crm_module` from `projects_details` where `id` = 0 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\ProjectDetailTrait.php", "line": 11}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 128}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 71}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00197, "duration_str": "1.97ms", "stmt_id": "\\app\\Http\\Traits\\ProjectDetailTrait.php:11", "connection": "osool_test_db", "start_percent": 98.727, "width_percent": 0.18}, {"sql": "select `id`, `created_at` from `users` where `project_id` = 0 and `user_type` = 'admin' and `status` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["0", "admin", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\UserTrait.php", "line": 256}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Traits\\UserTrait.php", "line": 267}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 158}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 74}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.00128, "duration_str": "1.28ms", "stmt_id": "\\app\\Http\\Traits\\UserTrait.php:256", "connection": "osool_test_db", "start_percent": 98.908, "width_percent": 0.117}, {"sql": "select count(*) as aggregate from `vendor_profiles` where `submit_status` = 'submit' and `application_status` = 'no_action' and `vendor_profiles`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["submit", "no_action"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 220}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 60}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00441, "duration_str": "4.41ms", "stmt_id": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php:220", "connection": "osool_test_db", "start_percent": 99.025, "width_percent": 0.403}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (21) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\PermissionServiceProvider.php", "line": 126}, {"index": 23, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\PermissionServiceProvider.php", "line": 137}, {"index": 27, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 55}, {"index": 29, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}], "duration": 0.0040999999999999995, "duration_str": "4.1ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:241", "connection": "osool_test_db", "start_percent": 99.428, "width_percent": 0.375}, {"sql": "select exists(select * from `projects_details` where `id` = 0 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["0", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 897}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_test_db", "start_percent": 99.803, "width_percent": 0.072}, {"sql": "select `id` from `users` where `project_id` = 0 and `user_type` = 'admin' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["0", "admin", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 928}, {"index": 14, "namespace": "view", "name": "f8d18d6f63e9bf36e98d3bb1b82c9e7d86b4c71c", "line": 13}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.0009699999999999999, "duration_str": "970μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:928", "connection": "osool_test_db", "start_percent": 99.876, "width_percent": 0.089}, {"sql": "select exists(select * from `projects_details` where `id` = 0 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["0", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 183}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_test_db", "start_percent": 99.964, "width_percent": 0.036}]}, "models": {"data": {"App\\Models\\Auth\\Role": 1, "App\\Models\\User": 2}, "count": 3}, "livewire": {"data": {"menu.aside-nav-list #": "array:7 [\n  \"data\" => array:10 [\n    \"user\" => App\\Models\\User {#3481\n      #connection: \"mysql\"\n      #table: \"users\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:78 [\n        \"id\" => 21\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$fDWCcrhfM04qytFJLBRif.FxX3wOI.YE0hyJ3HLOYureXOvSLc4ZG\"\n        \"name\" => \"Super Admin\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => \"123456789\"\n        \"profile_img\" => \"1641715388.png\"\n        \"emp_id\" => \"1234112\"\n        \"profession_id\" => null\n        \"emp_dept\" => \"Management\"\n        \"building_ids\" => null\n        \"contract_ids\" => \"\"\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => \"\"\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => \"1,2,3,4\"\n        \"role_cities\" => \"1,2,3,4\"\n        \"asset_categories\" => \"1,2,3,4\"\n        \"keeper_warehouses\" => null\n        \"properties\" => \"1\"\n        \"contracts\" => \"1\"\n        \"beneficiary\" => \"1\"\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"super_admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 0\n        \"project_id\" => 0\n        \"project_user_id\" => 0\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 0\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2022-03-03 13:52:49\"\n        \"modified_at\" => \"2025-07-28 16:45:32\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => null\n        \"crm_api_token\" => null\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #original: array:78 [\n        \"id\" => 21\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$fDWCcrhfM04qytFJLBRif.FxX3wOI.YE0hyJ3HLOYureXOvSLc4ZG\"\n        \"name\" => \"Super Admin\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => \"123456789\"\n        \"profile_img\" => \"1641715388.png\"\n        \"emp_id\" => \"1234112\"\n        \"profession_id\" => null\n        \"emp_dept\" => \"Management\"\n        \"building_ids\" => null\n        \"contract_ids\" => \"\"\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => \"\"\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => \"1,2,3,4\"\n        \"role_cities\" => \"1,2,3,4\"\n        \"asset_categories\" => \"1,2,3,4\"\n        \"keeper_warehouses\" => null\n        \"properties\" => \"1\"\n        \"contracts\" => \"1\"\n        \"beneficiary\" => \"1\"\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"super_admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 0\n        \"project_id\" => 0\n        \"project_user_id\" => 0\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 0\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2022-03-03 13:52:49\"\n        \"modified_at\" => \"2025-07-28 16:45:32\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => null\n        \"crm_api_token\" => null\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #changes: []\n      #casts: array:2 [\n        \"email_verified_at\" => \"datetime\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:2 [\n        \"projectDetails\" => null\n        \"roles\" => Illuminate\\Database\\Eloquent\\Collection {#5209\n          #items: array:1 [\n            0 => App\\Models\\Auth\\Role {#5254\n              #connection: \"mysql\"\n              #table: \"roles\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 1\n                \"name\" => \"super_admin\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2024-09-26 17:32:34\"\n                \"updated_at\" => \"2024-09-26 17:32:34\"\n              ]\n              #original: array:8 [\n                \"id\" => 1\n                \"name\" => \"super_admin\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2024-09-26 17:32:34\"\n                \"updated_at\" => \"2024-09-26 17:32:34\"\n                \"pivot_model_id\" => 21\n                \"pivot_role_id\" => 1\n                \"pivot_model_type\" => \"App\\Models\\User\"\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\MorphPivot {#5255\n                  #connection: \"mysql\"\n                  #table: \"model_has_roles\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:3 [\n                    \"model_id\" => 21\n                    \"role_id\" => 1\n                    \"model_type\" => \"App\\Models\\User\"\n                  ]\n                  #original: array:3 [\n                    \"model_id\" => 21\n                    \"role_id\" => 1\n                    \"model_type\" => \"App\\Models\\User\"\n                  ]\n                  #changes: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dates: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  +timestamps: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: App\\Models\\User {#5135\n                    #connection: \"mysql\"\n                    #table: \"users\"\n                    #primaryKey: \"id\"\n                    #keyType: \"int\"\n                    +incrementing: true\n                    #with: []\n                    #withCount: []\n                    +preventsLazyLoading: false\n                    #perPage: 15\n                    +exists: false\n                    +wasRecentlyCreated: false\n                    #escapeWhenCastingToString: false\n                    #attributes: []\n                    #original: []\n                    #changes: []\n                    #casts: array:2 [\n                      \"email_verified_at\" => \"datetime\"\n                      \"deleted_at\" => \"datetime\"\n                    ]\n                    #classCastCache: []\n                    #attributeCastCache: []\n                    #dates: []\n                    #dateFormat: null\n                    #appends: []\n                    #dispatchesEvents: []\n                    #observables: []\n                    #relations: []\n                    #touches: []\n                    +timestamps: true\n                    #hidden: array:2 [\n                      0 => \"password\"\n                      1 => \"remember_token\"\n                    ]\n                    #visible: []\n                    #fillable: array:62 [\n                      0 => \"allow_akaunting\"\n                      1 => \"email\"\n                      2 => \"password\"\n                      3 => \"name\"\n                      4 => \"first_name\"\n                      5 => \"last_name\"\n                      6 => \"apartment\"\n                      7 => \"unit_receival_date\"\n                      8 => \"later_booking_alert\"\n                      9 => \"phone\"\n                      10 => \"profile_img\"\n                      11 => \"address\"\n                      12 => \"country_id\"\n                      13 => \"city_id\"\n                      14 => \"role_regions\"\n                      15 => \"role_cities\"\n                      16 => \"asset_categories\"\n                      17 => \"properties\"\n                      18 => \"contracts\"\n                      19 => \"beneficiary\"\n                      20 => \"service_provider\"\n                      21 => \"user_type\"\n                      22 => \"project_id\"\n                      23 => \"project_user_id\"\n                      24 => \"created_by\"\n                      25 => \"status\"\n                      26 => \"user_privileges\"\n                      27 => \"approved_max_amount\"\n                      28 => \"emp_id\"\n                      29 => \"profession_id\"\n                      30 => \"emp_dept\"\n                      31 => \"building_ids\"\n                      32 => \"contract_ids\"\n                      33 => \"supervisor_id\"\n                      34 => \"sp_admin_id\"\n                      35 => \"langForSms\"\n                      36 => \"deleted_at\"\n                      37 => \"otp\"\n                      38 => \"temp_password\"\n                      39 => \"otp_for_password\"\n                      40 => \"otp_for_password_verified\"\n                      41 => \"temp_phone_number\"\n                      42 => \"favorite_language\"\n                      43 => \"is_subcontractors_worker\"\n                      44 => \"keeper_warehouses\"\n                      45 => \"save_later_date\"\n                      46 => \"first_login\"\n                      47 => \"is_unit_link\"\n                      48 => \"akaunting_vendor_id\"\n                      49 => \"akaunting_customer_id\"\n                      50 => \"crm_api_token\"\n                      51 => \"workspace_slug\"\n                      52 => \"is_bma_area_manager\"\n                      53 => \"assigned_workers\"\n                      54 => \"sleep_mode\"\n                      55 => \"offline_mode\"\n                      56 => \"attendance_mandatory\"\n                      57 => \"admin_level\"\n                      58 => \"role\"\n                      59 => \"attendance_target\"\n                      60 => \"salary\"\n                      61 => \"show_extra_info\"\n                    ]\n                    #guarded: array:1 [\n                      0 => \"*\"\n                    ]\n                    #rememberTokenName: \"remember_token\"\n                    #accessToken: null\n                    #forceDeleting: false\n                    #excludedAttributes: []\n                    +auditEvent: null\n                    +auditCustomOld: null\n                    +auditCustomNew: null\n                    +isCustomEvent: false\n                    +preloadedResolverData: []\n                    -roleClass: null\n                    -permissionClass: null\n                    -wildcardClass: null\n                  }\n                  #foreignKey: \"model_id\"\n                  #relatedKey: \"role_id\"\n                  #morphType: \"model_type\"\n                  #morphClass: \"App\\Models\\User\"\n                }\n              ]\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -permissionClass: null\n              -wildcardClass: null\n            }\n          ]\n          #escapeWhenCastingToString: false\n        }\n      ]\n      #touches: []\n      +timestamps: true\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: array:62 [\n        0 => \"allow_akaunting\"\n        1 => \"email\"\n        2 => \"password\"\n        3 => \"name\"\n        4 => \"first_name\"\n        5 => \"last_name\"\n        6 => \"apartment\"\n        7 => \"unit_receival_date\"\n        8 => \"later_booking_alert\"\n        9 => \"phone\"\n        10 => \"profile_img\"\n        11 => \"address\"\n        12 => \"country_id\"\n        13 => \"city_id\"\n        14 => \"role_regions\"\n        15 => \"role_cities\"\n        16 => \"asset_categories\"\n        17 => \"properties\"\n        18 => \"contracts\"\n        19 => \"beneficiary\"\n        20 => \"service_provider\"\n        21 => \"user_type\"\n        22 => \"project_id\"\n        23 => \"project_user_id\"\n        24 => \"created_by\"\n        25 => \"status\"\n        26 => \"user_privileges\"\n        27 => \"approved_max_amount\"\n        28 => \"emp_id\"\n        29 => \"profession_id\"\n        30 => \"emp_dept\"\n        31 => \"building_ids\"\n        32 => \"contract_ids\"\n        33 => \"supervisor_id\"\n        34 => \"sp_admin_id\"\n        35 => \"langForSms\"\n        36 => \"deleted_at\"\n        37 => \"otp\"\n        38 => \"temp_password\"\n        39 => \"otp_for_password\"\n        40 => \"otp_for_password_verified\"\n        41 => \"temp_phone_number\"\n        42 => \"favorite_language\"\n        43 => \"is_subcontractors_worker\"\n        44 => \"keeper_warehouses\"\n        45 => \"save_later_date\"\n        46 => \"first_login\"\n        47 => \"is_unit_link\"\n        48 => \"akaunting_vendor_id\"\n        49 => \"akaunting_customer_id\"\n        50 => \"crm_api_token\"\n        51 => \"workspace_slug\"\n        52 => \"is_bma_area_manager\"\n        53 => \"assigned_workers\"\n        54 => \"sleep_mode\"\n        55 => \"offline_mode\"\n        56 => \"attendance_mandatory\"\n        57 => \"admin_level\"\n        58 => \"role\"\n        59 => \"attendance_target\"\n        60 => \"salary\"\n        61 => \"show_extra_info\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #rememberTokenName: \"remember_token\"\n      #accessToken: null\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n    }\n    \"hasAdmin\" => 215\n    \"projectId\" => null\n    \"project\" => null\n    \"workOrderMenuItemColor\" => null\n    \"flagWorkorderSidebarMenu\" => false\n    \"userPrivileges\" => null\n    \"closedWorkOrderCount\" => null\n    \"maintenanceRequestCount\" => 0\n    \"vendorRegistrationApplicationRequests\" => 2\n  ]\n  \"oldData\" => null\n  \"actionQueue\" => null\n  \"name\" => \"menu.aside-nav-list\"\n  \"view\" => \"livewire.menu.aside-nav-list\"\n  \"component\" => \"App\\Http\\Livewire\\Menu\\AsideNavList\"\n  \"id\" => null\n]"}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"locale": "en", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "JLVDNTVcHVHZcuvwieTVnjqYHzf8uxLyTkOHH9U2", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/workspace/projects\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "21", "plain_user_password": "123456"}, "request": {"path_info": "/workspace/projects", "status_code": "<pre class=sf-dump id=sf-dump-1966413767 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1966413767\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1283551875 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1283551875\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1018234394 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1018234394\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1243253511 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://osool-b2g.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6ImdQaWVCTExFK05VdndteUZsQzYza0E9PSIsInZhbHVlIjoiZ0ZVMVVQSFgzUFBHeWI3WUpiZXpQdGMwUktETmxQcEQweUw5S3VEOE9iU2JFUXkyUTdkYmI0RWQxa0paeGlFVG5UQ1BJWnlKa2R5OHh2T05mcFFGZjR2OStpOEhMemFZV1czMVVWSmtqbW9sT0R5cS9JeW5HVXRNb2RTQU5TeU0iLCJtYWMiOiI1MGQzMzdmNGQ5OTFiYzQ3OGNkZjRjYTU4YWI3MTA0NzgwYWQzMWJlYmY4NjZmMTU0MTdmYzI4OWNmY2Y0ZDNhIiwidGFnIjoiIn0%3D; osool_session=Q9ENb1pxXvsqXWmGGn6KKAl1C2FYfkssqr1seSlw</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1243253511\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1230434137 data-indent-pad=\"  \"><span class=sf-dump-note>array:40</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://osool-b2g.test/login</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6ImdQaWVCTExFK05VdndteUZsQzYza0E9PSIsInZhbHVlIjoiZ0ZVMVVQSFgzUFBHeWI3WUpiZXpQdGMwUktETmxQcEQweUw5S3VEOE9iU2JFUXkyUTdkYmI0RWQxa0paeGlFVG5UQ1BJWnlKa2R5OHh2T05mcFFGZjR2OStpOEhMemFZV1czMVVWSmtqbW9sT0R5cS9JeW5HVXRNb2RTQU5TeU0iLCJtYWMiOiI1MGQzMzdmNGQ5OTFiYzQ3OGNkZjRjYTU4YWI3MTA0NzgwYWQzMWJlYmY4NjZmMTU0MTdmYzI4OWNmY2Y0ZDNhIiwidGFnIjoiIn0%3D; osool_session=Q9ENb1pxXvsqXWmGGn6KKAl1C2FYfkssqr1seSlw</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52093</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/workspace/projects</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/workspace/projects</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.5094</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1230434137\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-477100303 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JLVDNTVcHVHZcuvwieTVnjqYHzf8uxLyTkOHH9U2</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-477100303\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 13:46:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlNTQXovTWJET0FuTmxvUndyZHhUN3c9PSIsInZhbHVlIjoiRzZHS3c0YnhScDN5ZTJDL2xZZGVBbWJiUmUzdGNmczNSN2hQUm1GOUZMeko2STRZOFM2dllhRnBQTi80VGhDc0liOUcvUEpKa2haWDk0QThHMy9iQnk4VFJac0NJYzlJRFN5TzFvQ0pHNG8zVVNta0xpYWNZamVDczR1T2VQNy8iLCJtYWMiOiI3MDAyMmE4NjA0YzllMWE4NzRhYTUwMzRkM2Q0N2Y5NTZhOGFiNWYzNWRiOTExOWQ1MTYzZmVkMmRhM2MyNTY5IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:46:54 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6Ik5xelEyL0pKZEFWK1NKdTlXK09EbVE9PSIsInZhbHVlIjoiZkxiQU83VGovd3NQSDA4aGxKc0dFU1YxMllHWUhYTzczcFErQ2ZiZ0lVY05IRWhOUFVYdUFYQklCc3ovWi9BV3JoZm9WQWxCcUQreG95bU9FWXRTMkNtS2VqM2xNdWNHMG42QWFJWm9hT3kweEZnTXNoT25LOGdYQ1hINlpUa3AiLCJtYWMiOiI3NDM4MWUzZWUyMzAxZGQ1ODgwYzFmMGU5NTVhNGUzOWY2OWU1ZTE2MTM2NDE3N2UzZTdjODNjMDgxOWQ4MzhhIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:46:54 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlNTQXovTWJET0FuTmxvUndyZHhUN3c9PSIsInZhbHVlIjoiRzZHS3c0YnhScDN5ZTJDL2xZZGVBbWJiUmUzdGNmczNSN2hQUm1GOUZMeko2STRZOFM2dllhRnBQTi80VGhDc0liOUcvUEpKa2haWDk0QThHMy9iQnk4VFJac0NJYzlJRFN5TzFvQ0pHNG8zVVNta0xpYWNZamVDczR1T2VQNy8iLCJtYWMiOiI3MDAyMmE4NjA0YzllMWE4NzRhYTUwMzRkM2Q0N2Y5NTZhOGFiNWYzNWRiOTExOWQ1MTYzZmVkMmRhM2MyNTY5IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:46:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6Ik5xelEyL0pKZEFWK1NKdTlXK09EbVE9PSIsInZhbHVlIjoiZkxiQU83VGovd3NQSDA4aGxKc0dFU1YxMllHWUhYTzczcFErQ2ZiZ0lVY05IRWhOUFVYdUFYQklCc3ovWi9BV3JoZm9WQWxCcUQreG95bU9FWXRTMkNtS2VqM2xNdWNHMG42QWFJWm9hT3kweEZnTXNoT25LOGdYQ1hINlpUa3AiLCJtYWMiOiI3NDM4MWUzZWUyMzAxZGQ1ODgwYzFmMGU5NTVhNGUzOWY2OWU1ZTE2MTM2NDE3N2UzZTdjODNjMDgxOWQ4MzhhIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:46:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1416950282 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JLVDNTVcHVHZcuvwieTVnjqYHzf8uxLyTkOHH9U2</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/workspace/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>21</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1416950282\", {\"maxDepth\":0})</script>\n"}}