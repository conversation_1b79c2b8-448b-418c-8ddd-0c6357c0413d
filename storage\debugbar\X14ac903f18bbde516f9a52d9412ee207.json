{"__meta": {"id": "X14ac903f18bbde516f9a52d9412ee207", "datetime": "2025-07-28 16:49:27", "utime": 1753710567.807119, "method": "GET", "uri": "/workspace/adminProjectsListAjax?search_text=test&_=1753710560025", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 10, "messages": [{"message": "[16:49:27] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753710567.57745, "xdebug_link": null, "collector": "log"}, {"message": "[16:49:27] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2418", "message_html": null, "is_string": false, "label": "warning", "time": 1753710567.673178, "xdebug_link": null, "collector": "log"}, {"message": "[16:49:27] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2478", "message_html": null, "is_string": false, "label": "warning", "time": 1753710567.673303, "xdebug_link": null, "collector": "log"}, {"message": "[16:49:27] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3619", "message_html": null, "is_string": false, "label": "warning", "time": 1753710567.674335, "xdebug_link": null, "collector": "log"}, {"message": "[16:49:27] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710567.704565, "xdebug_link": null, "collector": "log"}, {"message": "[16:49:27] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710567.722308, "xdebug_link": null, "collector": "log"}, {"message": "[16:49:27] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710567.739171, "xdebug_link": null, "collector": "log"}, {"message": "[16:49:27] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710567.75314, "xdebug_link": null, "collector": "log"}, {"message": "[16:49:27] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710567.773717, "xdebug_link": null, "collector": "log"}, {"message": "[16:49:27] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": 1753710567.795141, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753710567.028149, "end": 1753710567.807151, "duration": 0.7790021896362305, "duration_str": "779ms", "measures": [{"label": "Booting", "start": 1753710567.028149, "relative_start": 0, "end": 1753710567.545118, "relative_end": 1753710567.545118, "duration": 0.5169692039489746, "duration_str": "517ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753710567.545129, "relative_start": 0.5169801712036133, "end": 1753710567.807153, "relative_end": 1.9073486328125e-06, "duration": 0.26202392578125, "duration_str": "262ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 37897544, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET workspace/adminProjectsListAjax/{id?}", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController@adminProjectListAjax", "as": "workspace.list_projects.ajax", "namespace": "App\\Http\\Controllers\\Admin\\Workspace", "prefix": "/workspace", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php&line=457\">\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:457-528</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.10296000000000001, "accumulated_duration_str": "103ms", "statements": [{"sql": "select * from `users` where `id` = 21 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0033399999999999997, "duration_str": "3.34ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 3.244}, {"sql": "select count(*) as aggregate from (select `projects_details`.*, `user_projects`.`status` from `projects_details` left join `user_projects` on `user_projects`.`project_id` = `projects_details`.`id` where (`projects_details`.`project_name` LIKE '%test%' and `projects_details`.`deleted_at` is null and `projects_details`.`is_deleted` = '0') and `projects_details`.`user_id` = 21 and `projects_details`.`deleted_at` is null group by `projects_details`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["%test%", "0", "21"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 490}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00138, "duration_str": "1.38ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:490", "connection": "osool_test_db", "start_percent": 3.244, "width_percent": 1.34}, {"sql": "select count(*) as aggregate from `projects_details` left join `users` on `users`.`id` = `projects_details`.`user_id` where ((`projects_details`.`project_name` LIKE '%test%' and `projects_details`.`deleted_at` is null and `projects_details`.`is_deleted` = 0) or (`projects_details`.`project_name_ar` LIKE '%test%' and `projects_details`.`deleted_at` is null and `projects_details`.`is_deleted` = 0) and `projects_details`.`deleted_at` is null) and `projects_details`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["%test%", "0", "%test%", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 499}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:499", "connection": "osool_test_db", "start_percent": 4.584, "width_percent": 0.699}, {"sql": "select `projects_details`.*, `users`.`name` from `projects_details` left join `users` on `users`.`id` = `projects_details`.`user_id` where ((`projects_details`.`project_name` LIKE '%test%' and `projects_details`.`deleted_at` is null and `projects_details`.`is_deleted` = 0) or (`projects_details`.`project_name_ar` LIKE '%test%' and `projects_details`.`deleted_at` is null and `projects_details`.`is_deleted` = 0) and `projects_details`.`deleted_at` is null) and `projects_details`.`deleted_at` is null order by `id` desc limit 6 offset 0", "type": "query", "params": [], "bindings": ["%test%", "0", "%test%", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 499}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php:499", "connection": "osool_test_db", "start_percent": 5.284, "width_percent": 0.631}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 205 limit 1", "type": "query", "params": [], "bindings": ["admin", "205"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01409, "duration_str": "14.09ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 5.915, "width_percent": 13.685}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 204 limit 1", "type": "query", "params": [], "bindings": ["admin", "204"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.016210000000000002, "duration_str": "16.21ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 19.6, "width_percent": 15.744}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 203 limit 1", "type": "query", "params": [], "bindings": ["admin", "203"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.015220000000000001, "duration_str": "15.22ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 35.344, "width_percent": 14.782}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 201 limit 1", "type": "query", "params": [], "bindings": ["admin", "201"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01255, "duration_str": "12.55ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 50.126, "width_percent": 12.189}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 200 limit 1", "type": "query", "params": [], "bindings": ["admin", "200"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01891, "duration_str": "18.91ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 62.315, "width_percent": 18.366}, {"sql": "select * from `users` where `user_type` = 'admin' and `project_id` = 199 limit 1", "type": "query", "params": [], "bindings": ["admin", "199"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1005}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\WorkspaceController.php", "line": 504}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01989, "duration_str": "19.89ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1005", "connection": "osool_test_db", "start_percent": 80.682, "width_percent": 19.318}]}, "models": {"data": {"App\\Models\\ProjectsDetails": 6, "App\\Models\\User": 1}, "count": 7}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"locale": "en", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/_debugbar/open?id=X7e94adc4f7ebe7a9721c7228d65a3537&op=get\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "21", "plain_user_password": "Tarqeem21", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/workspace/adminProjectsListAjax", "status_code": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1214400565 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search_text</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1753710560025</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1214400565\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1429555423 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search_text</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1753710560025</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429555423\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-133807358 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/workspace/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IkFuSndKZE4yRFdEdS9sREIzbTh3WEE9PSIsInZhbHVlIjoiWW93WWhhM0p1OWk3bU5iKy9oaFdKM2hwVkJBcDR0QnhycThleSt0NE5qYmo4KzVVSDlPd3JRdXFTUW44anJRSTd3b1JJcnE5QmhpRXkwbWE1WjI4dVA0TlI5ckMySHlDUmhqN1BzMG9WS3VJN2hOS1M2WUpBbll1eGlhL0NydEsiLCJtYWMiOiIzMzdlOGYwMjcxOTVhZjc5Yjg1ZWU3ZmE0NjBkNTcwYWU1MDY3MGI5MGNhYjQyMjFhNTk0MmUyYzhhZWEzOTUxIiwidGFnIjoiIn0%3D; osool_session=977kDvLC5XHrT14VqfN7VYTsJyRz1PjEW96YoIqg</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-133807358\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1512985862 data-indent-pad=\"  \"><span class=sf-dump-note>array:40</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/workspace/projects</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IkFuSndKZE4yRFdEdS9sREIzbTh3WEE9PSIsInZhbHVlIjoiWW93WWhhM0p1OWk3bU5iKy9oaFdKM2hwVkJBcDR0QnhycThleSt0NE5qYmo4KzVVSDlPd3JRdXFTUW44anJRSTd3b1JJcnE5QmhpRXkwbWE1WjI4dVA0TlI5ckMySHlDUmhqN1BzMG9WS3VJN2hOS1M2WUpBbll1eGlhL0NydEsiLCJtYWMiOiIzMzdlOGYwMjcxOTVhZjc5Yjg1ZWU3ZmE0NjBkNTcwYWU1MDY3MGI5MGNhYjQyMjFhNTk0MmUyYzhhZWEzOTUxIiwidGFnIjoiIn0%3D; osool_session=977kDvLC5XHrT14VqfN7VYTsJyRz1PjEW96YoIqg</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52369</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"32 characters\">/workspace/adminProjectsListAjax</span>\"\n  \"<span class=sf-dump-key>REDIRECT_QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"32 characters\">search_text=test&amp;_=1753710560025</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"32 characters\">search_text=test&amp;_=1753710560025</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"65 characters\">/workspace/adminProjectsListAjax?search_text=test&amp;_=1753710560025</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753710567.0281</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753710567</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1512985862\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-537377222 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-537377222\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-616184330 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 13:49:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImNBSnBpSmR3cjFUTnFhMjlEMjNHZ0E9PSIsInZhbHVlIjoiVGJaaHRrQ2JrakF1eCs3L1p4RDNNdjlWZ2FHTGg4UnQvcmxrN2ppaURoVWl1RC9zUmpDL2RIWFgreUJnWVFLdmpVcG1pNHFlWHJzSnNzdlkxRnZXYS9WY0xVNkVkK2JlU0I2bTFEcDN2UmY3Z3hxNWY0ZnA4a0hrRG80bzNyK3MiLCJtYWMiOiI1ZDAxOWQ0NTgxODljNmY4Y2Q1N2I5ZTRkYjFkODk2MDIxNjNkMDY5NGEzMzlmMjFjZTNkNmNlZDBhZGY2MWU0IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:49:27 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IlJ2L0dqTzZ5RzV5VzdBYlVDSHdyd3c9PSIsInZhbHVlIjoiUmYrcnVTTWY5SnlqOFJZYW9idGdRSUNoLzlla1JENzZWL1R6WjFIb2h2RHN3UE8zS1NEYjdFQnVrL1F1aktjMGxZR0lkM3ZOVDhQY0liTmJmTzRqZFdsZjl4ZVNwSnRtR0R1czRnRWgvTkxSMnRFbWFaYkZ4RXlFK2JFN2dGYUwiLCJtYWMiOiI4YmQ4NDUyMGNiZGVjNjE4YmQ2NWM3MDEwNGY4YzMxMWZhZTU4NDJjOTExNjVmMGFhM2JkMWNlM2M3ZTQ1MGJjIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:49:27 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImNBSnBpSmR3cjFUTnFhMjlEMjNHZ0E9PSIsInZhbHVlIjoiVGJaaHRrQ2JrakF1eCs3L1p4RDNNdjlWZ2FHTGg4UnQvcmxrN2ppaURoVWl1RC9zUmpDL2RIWFgreUJnWVFLdmpVcG1pNHFlWHJzSnNzdlkxRnZXYS9WY0xVNkVkK2JlU0I2bTFEcDN2UmY3Z3hxNWY0ZnA4a0hrRG80bzNyK3MiLCJtYWMiOiI1ZDAxOWQ0NTgxODljNmY4Y2Q1N2I5ZTRkYjFkODk2MDIxNjNkMDY5NGEzMzlmMjFjZTNkNmNlZDBhZGY2MWU0IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:49:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IlJ2L0dqTzZ5RzV5VzdBYlVDSHdyd3c9PSIsInZhbHVlIjoiUmYrcnVTTWY5SnlqOFJZYW9idGdRSUNoLzlla1JENzZWL1R6WjFIb2h2RHN3UE8zS1NEYjdFQnVrL1F1aktjMGxZR0lkM3ZOVDhQY0liTmJmTzRqZFdsZjl4ZVNwSnRtR0R1czRnRWgvTkxSMnRFbWFaYkZ4RXlFK2JFN2dGYUwiLCJtYWMiOiI4YmQ4NDUyMGNiZGVjNjE4YmQ2NWM3MDEwNGY4YzMxMWZhZTU4NDJjOTExNjVmMGFhM2JkMWNlM2M3ZTQ1MGJjIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:49:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-616184330\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-541504498 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"80 characters\">http://osool-b2g.test/_debugbar/open?id=X7e94adc4f7ebe7a9721c7228d65a3537&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>21</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Tarqeem21</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-541504498\", {\"maxDepth\":0})</script>\n"}}