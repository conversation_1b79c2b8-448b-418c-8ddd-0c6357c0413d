{"__meta": {"id": "Xdcd71dd0a5750383b9e73234cf432024", "datetime": "2025-07-28 16:50:31", "utime": **********.971829, "method": "GET", "uri": "/user/userListAjax?search_text=&page_length=10&_=1753710630642", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 17, "messages": [{"message": "[16:50:31] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.806488, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:31] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.84462, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:31] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.844672, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:31] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.844711, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:31] LOG.warning: Optional parameter $serviceProviderId declared before required parameter $search is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\UserTrait.php on line 328", "message_html": null, "is_string": false, "label": "warning", "time": **********.846598, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:31] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2418", "message_html": null, "is_string": false, "label": "warning", "time": **********.911081, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:31] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2478", "message_html": null, "is_string": false, "label": "warning", "time": **********.91121, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:31] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3619", "message_html": null, "is_string": false, "label": "warning", "time": **********.912049, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:31] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": **********.961399, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:31] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": **********.961547, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:31] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": **********.961637, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:31] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": **********.961729, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:31] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": **********.961809, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:31] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": **********.961885, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:31] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": **********.961963, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:31] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": **********.96204, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:31] LOG.warning: pathinfo(): Passing null to parameter #1 ($path) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\ImagesUploadHelper.php on line 391", "message_html": null, "is_string": false, "label": "warning", "time": **********.962116, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753710630.781878, "end": **********.971872, "duration": 1.1899940967559814, "duration_str": "1.19s", "measures": [{"label": "Booting", "start": 1753710630.781878, "relative_start": 0, "end": **********.779647, "relative_end": **********.779647, "duration": 0.9977691173553467, "duration_str": "998ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.779659, "relative_start": 0.9977810382843018, "end": **********.971874, "relative_end": 1.9073486328125e-06, "duration": 0.1922149658203125, "duration_str": "192ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 39440056, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET user/userListAjax/{id?}", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Admin\\User\\UserControllerNew@userListAjax", "as": "users.list.ajax", "namespace": "App\\Http\\Controllers\\Admin\\User", "prefix": "/user", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php&line=264\">\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php:264-475</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.052820000000000006, "accumulated_duration_str": "52.82ms", "statements": [{"sql": "select * from `users` where `id` = 21 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.01582, "duration_str": "15.82ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 29.951}, {"sql": "select count(*) as aggregate from `users` where `user_type` = 'admin' and `project_id` = '191' and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["admin", "191"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php", "line": 428}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0035299999999999997, "duration_str": "3.53ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php:428", "connection": "osool_test_db", "start_percent": 29.951, "width_percent": 6.683}, {"sql": "select count(*) as aggregate from (select `service_providers`.`global_sp`, `users`.*, `cities`.`name_en` as `city_name_en`, `cities`.`name_ar` as `city_name_ar`, `worker_professions`.`profession_en`, `worker_professions`.`profession_ar` from `users` left join `worker_professions` on `worker_professions`.`id` = `users`.`profession_id` left join `service_providers` on `service_providers`.`id` = `users`.`service_provider` left join `cities` on `cities`.`id` = `users`.`city_id` where `users`.`id` != 21 and ( `service_providers`.`global_sp` = '0' or `service_providers`.`global_sp` IS NULL )  and  ( users.user_type = 'admin' OR\nusers.user_type = 'admin_employee'  OR\n( users.user_type = 'sp_admin' AND service_providers.global_sp = 0 )   OR\nusers.user_type = 'supervisor' OR\nusers.user_type = 'building_manager' OR\nusers.user_type = 'building_manager_employee' OR\nusers.user_type = 'store_keeper' OR\nusers.user_type = 'sp_worker' OR users.user_type = 'team_leader'  )  and `project_user_id` = 6942 and  ( users.user_type = 'admin' OR\nusers.user_type = 'admin_employee'  OR\n( users.user_type = 'sp_admin' AND service_providers.global_sp = 0 )   OR\nusers.user_type = 'supervisor' OR\nusers.user_type = 'building_manager' OR\nusers.user_type = 'building_manager_employee' OR\nusers.user_type = 'store_keeper' OR\nusers.user_type = 'sp_worker'  OR users.user_type = 'team_leader' )  and `project_user_id` = 6942 and (`users`.`deleted_at` is null and `users`.`is_deleted` = 'no') and `users`.`deleted_at` is null group by `users`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["21", "6942", "6942", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php", "line": 459}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.01812, "duration_str": "18.12ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php:459", "connection": "osool_test_db", "start_percent": 36.634, "width_percent": 34.305}, {"sql": "select `service_providers`.`global_sp`, `users`.*, `cities`.`name_en` as `city_name_en`, `cities`.`name_ar` as `city_name_ar`, `worker_professions`.`profession_en`, `worker_professions`.`profession_ar` from `users` left join `worker_professions` on `worker_professions`.`id` = `users`.`profession_id` left join `service_providers` on `service_providers`.`id` = `users`.`service_provider` left join `cities` on `cities`.`id` = `users`.`city_id` where `users`.`id` != 21 and ( `service_providers`.`global_sp` = '0' or `service_providers`.`global_sp` IS NULL )  and  ( users.user_type = 'admin' OR\nusers.user_type = 'admin_employee'  OR\n( users.user_type = 'sp_admin' AND service_providers.global_sp = 0 )   OR\nusers.user_type = 'supervisor' OR\nusers.user_type = 'building_manager' OR\nusers.user_type = 'building_manager_employee' OR\nusers.user_type = 'store_keeper' OR\nusers.user_type = 'sp_worker' OR users.user_type = 'team_leader'  )  and `project_user_id` = 6942 and  ( users.user_type = 'admin' OR\nusers.user_type = 'admin_employee'  OR\n( users.user_type = 'sp_admin' AND service_providers.global_sp = 0 )   OR\nusers.user_type = 'supervisor' OR\nusers.user_type = 'building_manager' OR\nusers.user_type = 'building_manager_employee' OR\nusers.user_type = 'store_keeper' OR\nusers.user_type = 'sp_worker'  OR users.user_type = 'team_leader' )  and `project_user_id` = 6942 and (`users`.`deleted_at` is null and `users`.`is_deleted` = 'no') and `users`.`deleted_at` is null group by `users`.`id` order by `users`.`id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["21", "6942", "6942", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php", "line": 459}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.015349999999999999, "duration_str": "15.35ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\User\\UserControllerNew.php:459", "connection": "osool_test_db", "start_percent": 70.939, "width_percent": 29.061}]}, "models": {"data": {"App\\Models\\User": 10}, "count": 10}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"locale": "en", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/user/users-list\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "21", "plain_user_password": "Tarqeem21", "entered_project_id": "191", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/user/userListAjax", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-581851742 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search_text</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>page_length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1753710630642</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-581851742\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-374274912 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search_text</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>page_length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1753710630642</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-374274912\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-599812343 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://osool-b2g.test/user/users-list</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IjRhYno3YS9oOVgrZ2wzSS9XNExIeGc9PSIsInZhbHVlIjoiT2xRN2NqSWY3STlDZU9DbzhydS9VMkVVaVhnRng2YmF2K2Q4Q2VGeE9EZkZvb3FwWW1XdzZGaHd5QTgxOXQ3bk5pbXhoMTh4akNNaXNvRW1naStHUEtlWFp4QklIelN0cFRQWSthVGxucWtmTGRZd1BkMm5MVVZaN1VtL2ZoMzUiLCJtYWMiOiI0NDVhMWNjMjcwZjUwNzJkZTViNDZkYWM3ZTFjYTQwMDVhZGJiN2Y3MTBiODBlNjVjZWEwNmMyODJmMDgwM2VhIiwidGFnIjoiIn0%3D; osool_session=1T6u4Fv32lXbeR3RK8iZHWadNDZWkssYA8jb2HLz</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-599812343\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1772902578 data-indent-pad=\"  \"><span class=sf-dump-note>array:40</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://osool-b2g.test/user/users-list</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IjRhYno3YS9oOVgrZ2wzSS9XNExIeGc9PSIsInZhbHVlIjoiT2xRN2NqSWY3STlDZU9DbzhydS9VMkVVaVhnRng2YmF2K2Q4Q2VGeE9EZkZvb3FwWW1XdzZGaHd5QTgxOXQ3bk5pbXhoMTh4akNNaXNvRW1naStHUEtlWFp4QklIelN0cFRQWSthVGxucWtmTGRZd1BkMm5MVVZaN1VtL2ZoMzUiLCJtYWMiOiI0NDVhMWNjMjcwZjUwNzJkZTViNDZkYWM3ZTFjYTQwMDVhZGJiN2Y3MTBiODBlNjVjZWEwNmMyODJmMDgwM2VhIiwidGFnIjoiIn0%3D; osool_session=1T6u4Fv32lXbeR3RK8iZHWadNDZWkssYA8jb2HLz</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52511</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"18 characters\">/user/userListAjax</span>\"\n  \"<span class=sf-dump-key>REDIRECT_QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"43 characters\">search_text=&amp;page_length=10&amp;_=1753710630642</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"43 characters\">search_text=&amp;page_length=10&amp;_=1753710630642</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"62 characters\">/user/userListAjax?search_text=&amp;page_length=10&amp;_=1753710630642</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753710630.7819</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753710630</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1772902578\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2027569444 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2027569444\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1426684363 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 13:50:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImxvSWFNQjk2RmJPckdWYncrWGhad3c9PSIsInZhbHVlIjoiNHZPRnRHeGp5M1Z4SFhsL2NGME5XdmRiY1RFU0FadThLSFFHQTg2YXkxaHNJaFBJR2w0ejNER25XSyszZFlZQVE1NEdNTHhPek4rSkhVdHdCSjlKZjMrWXpHZkgyTGdMNWpIRmlLS09PZW1ZOEFJeDVVdWw1OVorNVZZeHBmY0oiLCJtYWMiOiJkMjgwMDBiZDZiZTJiMzJmZDY1OGI1YzZkODZiMzEyNTkwMDRkMTg0YmQzYWEwYzI2YTE3NWU4ZWU2NGNlMzgxIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:50:31 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IndiOWxhWXRyd1V1WkJqcG13aDJBK0E9PSIsInZhbHVlIjoic3lsaStheDdlejdFM1hqT1BxN1dOQkxCTWZ2MWFOc0ljcWxRUGxpN2ZOb0VMUG5KQVFuOHhmdHM4S1B3cGU1OC9aNnRJVFhNcHdtVkdpY0ZsRCtFQU1xdnlUQm15akE4bDhqcForV2FYMUhZZHVqQ0FnWE5nbFU3TDdBZkYwYTgiLCJtYWMiOiIzNjRiYmEwY2ZiZGNiNWQwMDJiZjE0ZmI1MjdhMWNmOGI5MGVjMDM5ODIzMWI5MzE0MjBhMTMxZWUyYjJiZmFlIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:50:31 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImxvSWFNQjk2RmJPckdWYncrWGhad3c9PSIsInZhbHVlIjoiNHZPRnRHeGp5M1Z4SFhsL2NGME5XdmRiY1RFU0FadThLSFFHQTg2YXkxaHNJaFBJR2w0ejNER25XSyszZFlZQVE1NEdNTHhPek4rSkhVdHdCSjlKZjMrWXpHZkgyTGdMNWpIRmlLS09PZW1ZOEFJeDVVdWw1OVorNVZZeHBmY0oiLCJtYWMiOiJkMjgwMDBiZDZiZTJiMzJmZDY1OGI1YzZkODZiMzEyNTkwMDRkMTg0YmQzYWEwYzI2YTE3NWU4ZWU2NGNlMzgxIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:50:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IndiOWxhWXRyd1V1WkJqcG13aDJBK0E9PSIsInZhbHVlIjoic3lsaStheDdlejdFM1hqT1BxN1dOQkxCTWZ2MWFOc0ljcWxRUGxpN2ZOb0VMUG5KQVFuOHhmdHM4S1B3cGU1OC9aNnRJVFhNcHdtVkdpY0ZsRCtFQU1xdnlUQm15akE4bDhqcForV2FYMUhZZHVqQ0FnWE5nbFU3TDdBZkYwYTgiLCJtYWMiOiIzNjRiYmEwY2ZiZGNiNWQwMDJiZjE0ZmI1MjdhMWNmOGI5MGVjMDM5ODIzMWI5MzE0MjBhMTMxZWUyYjJiZmFlIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:50:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1426684363\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1167800194 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://osool-b2g.test/user/users-list</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>21</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Tarqeem21</span>\"\n  \"<span class=sf-dump-key>entered_project_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">191</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1167800194\", {\"maxDepth\":0})</script>\n"}}