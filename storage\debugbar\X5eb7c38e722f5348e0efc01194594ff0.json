{"__meta": {"id": "X5eb7c38e722f5348e0efc01194594ff0", "datetime": "2025-07-28 16:52:16", "utime": 1753710736.892835, "method": "POST", "uri": "/logout", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[16:52:16] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753710736.789649, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753710736.223298, "end": 1753710736.892854, "duration": 0.6695559024810791, "duration_str": "670ms", "measures": [{"label": "Booting", "start": 1753710736.223298, "relative_start": 0, "end": 1753710736.762387, "relative_end": 1753710736.762387, "duration": 0.5390889644622803, "duration_str": "539ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753710736.762408, "relative_start": 0.5391099452972412, "end": 1753710736.892856, "relative_end": 1.9073486328125e-06, "duration": 0.1304478645324707, "duration_str": "130ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 37069784, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST logout", "middleware": "web", "controller": "App\\Http\\Controllers\\Auth\\LoginController@logout", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "logout", "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Auth\\LoginController.php&line=135\">\\app\\Http\\Controllers\\Auth\\LoginController.php:135-158</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.01714, "accumulated_duration_str": "17.14ms", "statements": [{"sql": "select * from `users` where `id` = 21 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 23}], "duration": 0.0070999999999999995, "duration_str": "7.1ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 41.424}, {"sql": "update `users` set `project_id` = 0, `project_user_id` = 0, `users`.`modified_at` = '2025-07-28 16:52:16' where `id` = 21 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["0", "0", "2025-07-28 16:52:16", "21"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 141}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0025, "duration_str": "2.5ms", "stmt_id": "\\app\\Http\\Controllers\\Auth\\LoginController.php:141", "connection": "osool_test_db", "start_percent": 41.424, "width_percent": 14.586}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 191 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["191"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\User.php", "line": 209}, {"index": 21, "namespace": null, "name": "\\app\\Models\\User.php", "line": 200}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 147}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "\\app\\Models\\User.php:209", "connection": "osool_test_db", "start_percent": 56.009, "width_percent": 5.018}, {"sql": "update `users` set `workspace_slug` = '', `crm_api_token` = '', `users`.`modified_at` = '2025-07-28 16:52:16' where `id` = 21", "type": "query", "params": [], "bindings": ["", "", "2025-07-28 16:52:16", "21"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 147}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00361, "duration_str": "3.61ms", "stmt_id": "\\app\\Http\\Controllers\\Auth\\LoginController.php:147", "connection": "osool_test_db", "start_percent": 61.027, "width_percent": 21.062}, {"sql": "insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('{\\\"workspace_slug\\\":\\\"memo\\\",\\\"crm_api_token\\\":\\\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************.F-HytyuBuf52RxpgRuHKyq9INjqBN5U6c4lL2F7q6fI\\\"}', '{\\\"workspace_slug\\\":null,\\\"crm_api_token\\\":null}', 'updated', 21, 'App\\Models\\User', 21, 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'http://osool-b2g.test/logout', '2025-07-28 16:52:16', '2025-07-28 16:52:16')", "type": "query", "params": [], "bindings": ["{&quot;workspace_slug&quot;:&quot;memo&quot;,&quot;crm_api_token&quot;:&quot;eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************.F-HytyuBuf52RxpgRuHKyq9INjqBN5U6c4lL2F7q6fI&quot;}", "{&quot;workspace_slug&quot;:null,&quot;crm_api_token&quot;:null}", "updated", "21", "App\\Models\\User", "21", "App\\Models\\User", "", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "http://osool-b2g.test/logout", "2025-07-28 16:52:16", "2025-07-28 16:52:16"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "\\vendor\\owen-it\\laravel-auditing\\src\\Auditor.php", "line": 83}, {"index": 25, "namespace": null, "name": "\\vendor\\owen-it\\laravel-auditing\\src\\AuditableObserver.php", "line": 99}, {"index": 26, "namespace": null, "name": "\\vendor\\owen-it\\laravel-auditing\\src\\AuditableObserver.php", "line": 49}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 147}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00307, "duration_str": "3.07ms", "stmt_id": "\\vendor\\owen-it\\laravel-auditing\\src\\Auditor.php:83", "connection": "osool_test_db", "start_percent": 82.089, "width_percent": 17.911}]}, "models": {"data": {"App\\Models\\ProjectsDetails": 1, "App\\Models\\User": 1}, "count": 2}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"locale": "en", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "g7Dn0dkBBOx6ah5jtIBKp4VpCyNcLrw6Ay9RmcAI", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/logout", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-995516443 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-995516443\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1100841262 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1100841262\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-713811517 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"237 characters\">http://osool-b2g.test/user/eyJpdiI6IkdCZkNlaHdEZXRCNCtyYTc2Lzdtdmc9PSIsInZhbHVlIjoiYXRUYW0rUTVjS01tYXhoOFNrOTZ3Zz09IiwibWFjIjoiOTVjY2U5YTJhMzg2MDU1MzdiNDY2MGMyNDY1YWJhMGQ5YzAyYjlkYWE0YzIyMTBhZmFmY2QzM2MyM2Q2MTYyMCIsInRhZyI6IiJ9/edit/role</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6InBXcGplU1BZbkJMTHFwWU1GYmNBQkE9PSIsInZhbHVlIjoia0ZsQ2lQbnFIc0pkT1lvb2FmRUgxNzNrZUhneHZYbVdsUVBZSHI5OFl1dDQvQllzN21IR3J0WFBhUkxOY3E2czB0MjFLWEw3WnJzT0l0YkdGL21pYW8rOTRvdk9HNCtHYzJiREpuclR5bzJ0VFM2VWRJbEpEUCt2WTNsamo0QTIiLCJtYWMiOiI0NThiOWI1NzU3ZWQ0OGVmYTFjYzg1Y2MxMGExZDNhZDdhZTcyNmZmNzY2N2FlMjE0NzdiYWZhYzdiNmZlOGY0IiwidGFnIjoiIn0%3D; osool_session=wO8Xj65mBJbhzSxBI3SnbXQCUyahYByTx8HSToLD</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-713811517\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1091455212 data-indent-pad=\"  \"><span class=sf-dump-note>array:43</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"237 characters\">http://osool-b2g.test/user/eyJpdiI6IkdCZkNlaHdEZXRCNCtyYTc2Lzdtdmc9PSIsInZhbHVlIjoiYXRUYW0rUTVjS01tYXhoOFNrOTZ3Zz09IiwibWFjIjoiOTVjY2U5YTJhMzg2MDU1MzdiNDY2MGMyNDY1YWJhMGQ5YzAyYjlkYWE0YzIyMTBhZmFmY2QzM2MyM2Q2MTYyMCIsInRhZyI6IiJ9/edit/role</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6InBXcGplU1BZbkJMTHFwWU1GYmNBQkE9PSIsInZhbHVlIjoia0ZsQ2lQbnFIc0pkT1lvb2FmRUgxNzNrZUhneHZYbVdsUVBZSHI5OFl1dDQvQllzN21IR3J0WFBhUkxOY3E2czB0MjFLWEw3WnJzT0l0YkdGL21pYW8rOTRvdk9HNCtHYzJiREpuclR5bzJ0VFM2VWRJbEpEUCt2WTNsamo0QTIiLCJtYWMiOiI0NThiOWI1NzU3ZWQ0OGVmYTFjYzg1Y2MxMGExZDNhZDdhZTcyNmZmNzY2N2FlMjE0NzdiYWZhYzdiNmZlOGY0IiwidGFnIjoiIn0%3D; osool_session=wO8Xj65mBJbhzSxBI3SnbXQCUyahYByTx8HSToLD</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52811</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"7 characters\">/logout</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"7 characters\">/logout</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753710736.2233</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753710736</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1091455212\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-528797872 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-528797872\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-696384501 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 13:52:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://osool-b2g.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"346 characters\">XSRF-TOKEN=eyJpdiI6IjY1SGdBWGhZUFhKUk5BdlhRZzNVdWc9PSIsInZhbHVlIjoiVFoyTm85QkRJVGJUK1RVb0d5TG9ZT1A1aHBIbzA2WXJxMFZ4NkVINWh2OTQ0Snc0Z2xjdGt2Q2hVVzFRc25vUiIsIm1hYyI6IjM1NWVlYTVmMzg5MDdkNzBiY2ZhMjA1OGMyMTAwZjhlOWVmMjRhYzkyODUwMTFjYmZlNWMyMmZjODc2ZTU5NzciLCJ0YWciOiIifQ%3D%3D; expires=Mon, 28-Jul-2025 15:52:16 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6ImVjU1RpakhEYytMNlpFdWhCVGRPcVE9PSIsInZhbHVlIjoiMnFWM0ZUYUpBdGFXZ05rdWJocUpSSnZXT3B1WW9FUGRPSXVLQ1FpK3ZJb2pHQUpqSEh4cHpxSkJoc1RHSWMwRENIRDUyZXdrWXhqc1RiSVg4UGdEMHkvMTFiL0xSWWNsM0J5TzNhWmlzOU1XM0FaTTFicjVTd240OVhjb0F2WTAiLCJtYWMiOiI5MTBlNjVmNDc4NTQ4M2VkYzM1N2Y0YWU1YjE5NDA1MDNiYTgwMWJmZTlkYTIzYTkyOTc1YzBmNjE4YjMxNjYyIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:52:16 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"318 characters\">XSRF-TOKEN=eyJpdiI6IjY1SGdBWGhZUFhKUk5BdlhRZzNVdWc9PSIsInZhbHVlIjoiVFoyTm85QkRJVGJUK1RVb0d5TG9ZT1A1aHBIbzA2WXJxMFZ4NkVINWh2OTQ0Snc0Z2xjdGt2Q2hVVzFRc25vUiIsIm1hYyI6IjM1NWVlYTVmMzg5MDdkNzBiY2ZhMjA1OGMyMTAwZjhlOWVmMjRhYzkyODUwMTFjYmZlNWMyMmZjODc2ZTU5NzciLCJ0YWciOiIifQ%3D%3D; expires=Mon, 28-Jul-2025 15:52:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6ImVjU1RpakhEYytMNlpFdWhCVGRPcVE9PSIsInZhbHVlIjoiMnFWM0ZUYUpBdGFXZ05rdWJocUpSSnZXT3B1WW9FUGRPSXVLQ1FpK3ZJb2pHQUpqSEh4cHpxSkJoc1RHSWMwRENIRDUyZXdrWXhqc1RiSVg4UGdEMHkvMTFiL0xSWWNsM0J5TzNhWmlzOU1XM0FaTTFicjVTd240OVhjb0F2WTAiLCJtYWMiOiI5MTBlNjVmNDc4NTQ4M2VkYzM1N2Y0YWU1YjE5NDA1MDNiYTgwMWJmZTlkYTIzYTkyOTc1YzBmNjE4YjMxNjYyIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:52:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-696384501\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1342125496 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g7Dn0dkBBOx6ah5jtIBKp4VpCyNcLrw6Ay9RmcAI</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1342125496\", {\"maxDepth\":0})</script>\n"}}