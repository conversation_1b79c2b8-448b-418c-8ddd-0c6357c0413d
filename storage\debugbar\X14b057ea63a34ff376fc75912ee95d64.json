{"__meta": {"id": "X14b057ea63a34ff376fc75912ee95d64", "datetime": "2025-07-28 16:45:53", "utime": **********.414099, "method": "POST", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[16:45:53] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.165543, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753710352.538675, "end": **********.414123, "duration": 0.8754479885101318, "duration_str": "875ms", "measures": [{"label": "Booting", "start": 1753710352.538675, "relative_start": 0, "end": **********.137668, "relative_end": **********.137668, "duration": 0.5989928245544434, "duration_str": "599ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.1377, "relative_start": 0.5990250110626221, "end": **********.414126, "relative_end": 2.86102294921875e-06, "duration": 0.276425838470459, "duration_str": "276ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 38311048, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "middleware": "web", "controller": "App\\Http\\Controllers\\Auth\\LoginController@login", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Auth\\LoginController.php&line=78\">\\app\\Http\\Controllers\\Auth\\LoginController.php:78-133</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02349, "accumulated_duration_str": "23.49ms", "statements": [{"sql": "select * from `users` where `email` = '<EMAIL>' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 133}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 377}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 80}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.0179, "duration_str": "17.9ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:133", "connection": "osool_test_db", "start_percent": 0, "width_percent": 76.203}, {"sql": "select exists(select * from `projects_details` where `id` = 191 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["191", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_test_db", "start_percent": 76.203, "width_percent": 3.491}, {"sql": "select exists(select * from `crm_user` where `user_id` = 6942) as `exists`", "type": "query", "params": [], "bindings": ["6942"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_test_db", "start_percent": 79.693, "width_percent": 2.937}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, '', **********, **********, '{\\\"uuid\\\":\\\"55634c8b-7e23-4b61-8b35-768778a384f2\\\",\\\"displayName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"command\\\":\\\"O:24:\\\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\\\":12:{s:8:\\\\\"\\u0000*\\u0000email\\\\\";s:20:\\\\\"<EMAIL>\\\\\";s:11:\\\\\"\\u0000*\\u0000password\\\\\";s:6:\\\\\"123456\\\\\";s:3:\\\\\"job\\\\\";N;s:10:\\\\\"connection\\\\\";N;s:5:\\\\\"queue\\\\\";N;s:15:\\\\\"chainConnection\\\\\";N;s:10:\\\\\"chainQueue\\\\\";N;s:19:\\\\\"chainCatchCallbacks\\\\\";N;s:5:\\\\\"delay\\\\\";N;s:11:\\\\\"afterCommit\\\\\";N;s:10:\\\\\"middleware\\\\\";a:0:{}s:7:\\\\\"chained\\\\\";a:0:{}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", "0", "", "**********", "**********", "{&quot;uuid&quot;:&quot;55634c8b-7e23-4b61-8b35-768778a384f2&quot;,&quot;displayName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;job&quot;:&quot;Illuminate\\\\Queue\\\\CallQueuedHandler@call&quot;,&quot;maxTries&quot;:null,&quot;maxExceptions&quot;:null,&quot;failOnTimeout&quot;:false,&quot;backoff&quot;:null,&quot;timeout&quot;:null,&quot;retryUntil&quot;:null,&quot;data&quot;:{&quot;commandName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;command&quot;:&quot;O:24:\\&quot;App\\\\Jobs\\\\ProcessCrmLogin\\&quot;:12:{s:8:\\&quot;\\u0000*\\u0000email\\&quot;;s:20:\\&quot;<EMAIL>\\&quot;;s:11:\\&quot;\\u0000*\\u0000password\\&quot;;s:6:\\&quot;123456\\&quot;;s:3:\\&quot;job\\&quot;;N;s:10:\\&quot;connection\\&quot;;N;s:5:\\&quot;queue\\&quot;;N;s:15:\\&quot;chainConnection\\&quot;;N;s:10:\\&quot;chainQueue\\&quot;;N;s:19:\\&quot;chainCatchCallbacks\\&quot;;N;s:5:\\&quot;delay\\&quot;;N;s:11:\\&quot;afterCommit\\&quot;;N;s:10:\\&quot;middleware\\&quot;;a:0:{}s:7:\\&quot;chained\\&quot;;a:0:{}}&quot;}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 181}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 317}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 253}], "duration": 0.00408, "duration_str": "4.08ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php:181", "connection": "osool_test_db", "start_percent": 82.631, "width_percent": 17.369}]}, "models": {"data": {"App\\Models\\User": 1}, "count": 1}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"locale": "en", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "56eCVpAm7Z7Njid7pxgzHlc33GSeRPOdapi4KDAX", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/login\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "6942", "plain_user_password": "123456"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-542777645 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-542777645\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-60392768 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-60392768\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1973521170 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56eCVpAm7Z7Njid7pxgzHlc33GSeRPOdapi4KDAX</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1973521170\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-125361046 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">94</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://osool-b2g.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6Ikt0TkRvNnZFdkxPNkI1V051TnZqRFE9PSIsInZhbHVlIjoiTFhGbTByZmVJVzByVlpBQmpueUw2alZjWWhlWmlmRmx4QzdTZHU1OFBsVTR2ZUlxNEVCYk1FNy9UTXN4b0lFdXl4UUR2NUdWdWRvditpemg5SFZueG9yeVl4TjZMUFJYb1YrVEtVaVh2NDRTalRmSVlUMXk0YmNLbWRJQ2IrUWUiLCJtYWMiOiJjNjQ0YTcyZDMyZjViOTI3ZmIwZWQyM2ZkMTk1ODBjYjZiMGIzZmEzZjgyYTQzZDRlYjE2MmUwMmYxYjRhOWE3IiwidGFnIjoiIn0%3D; osool_session=BkpS6RfKR3CpB8LqoBuziPA4J399VRDkXmrssiqv</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-125361046\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-409044934 data-indent-pad=\"  \"><span class=sf-dump-note>array:43</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">94</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://osool-b2g.test/login</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6Ikt0TkRvNnZFdkxPNkI1V051TnZqRFE9PSIsInZhbHVlIjoiTFhGbTByZmVJVzByVlpBQmpueUw2alZjWWhlWmlmRmx4QzdTZHU1OFBsVTR2ZUlxNEVCYk1FNy9UTXN4b0lFdXl4UUR2NUdWdWRvditpemg5SFZueG9yeVl4TjZMUFJYb1YrVEtVaVh2NDRTalRmSVlUMXk0YmNLbWRJQ2IrUWUiLCJtYWMiOiJjNjQ0YTcyZDMyZjViOTI3ZmIwZWQyM2ZkMTk1ODBjYjZiMGIzZmEzZjgyYTQzZDRlYjE2MmUwMmYxYjRhOWE3IiwidGFnIjoiIn0%3D; osool_session=BkpS6RfKR3CpB8LqoBuziPA4J399VRDkXmrssiqv</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51941</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753710352.5387</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753710352</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-409044934\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2066992776 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56eCVpAm7Z7Njid7pxgzHlc33GSeRPOdapi4KDAX</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2066992776\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-681195059 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 13:45:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://osool-b2g.test/dashboards/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IktuS1R3d2VsbFNVOFNFR0JoSnZvZ2c9PSIsInZhbHVlIjoiQzBhWGNzSXkvbnlDZk5GNXFoRlJhRVFBVndzdzh4UzlzV2JMaCtPa2VtakI5VDJlSTJ0YkVwZ2M5YURHMktBZW51T0xWSVJzaWlBbmVvc0hPcEtTeUZxeEtkWUw2SGk4dCtzcVJRWmtIbGZRamY1MitrVnAyVVJJN0NRTCtKbUsiLCJtYWMiOiI3ZjQ2YWQ4MmIxZGI5NjNhMzRmNjBkNjYyZWVjNTYzOWNjYmM1MWNkOGYyZjA4YTdlNjMwNmI5N2JiZGE2MjNjIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:45:53 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IjFzWkxEUHlNYlZ0RjFhQTE3MWJPU1E9PSIsInZhbHVlIjoidVV5c0N4d2ljbnRvTm51aEE3VVk4d3lrckpUbTN4RWtCQWJ5NDVFQnNXWW9OOGxsOVRDMkJWQWlUZUtrZFNHMGpDTUwrRVNjLytOWXN4TFVNbXk3dTBSVVVON2w2SEhxNXRyMXliSkpjYVBOQTVOUlUzVE9FZzFXYkdMVDViSkoiLCJtYWMiOiIxNjhjZTcxYzlhNDdiY2VmNGQ4MjQ0OTUxNDBiODg0NWZkYzY2MzZhMmZlNjA3NDk1MDNmNWNlNmM3NjkyZGJiIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:45:53 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IktuS1R3d2VsbFNVOFNFR0JoSnZvZ2c9PSIsInZhbHVlIjoiQzBhWGNzSXkvbnlDZk5GNXFoRlJhRVFBVndzdzh4UzlzV2JMaCtPa2VtakI5VDJlSTJ0YkVwZ2M5YURHMktBZW51T0xWSVJzaWlBbmVvc0hPcEtTeUZxeEtkWUw2SGk4dCtzcVJRWmtIbGZRamY1MitrVnAyVVJJN0NRTCtKbUsiLCJtYWMiOiI3ZjQ2YWQ4MmIxZGI5NjNhMzRmNjBkNjYyZWVjNTYzOWNjYmM1MWNkOGYyZjA4YTdlNjMwNmI5N2JiZGE2MjNjIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:45:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IjFzWkxEUHlNYlZ0RjFhQTE3MWJPU1E9PSIsInZhbHVlIjoidVV5c0N4d2ljbnRvTm51aEE3VVk4d3lrckpUbTN4RWtCQWJ5NDVFQnNXWW9OOGxsOVRDMkJWQWlUZUtrZFNHMGpDTUwrRVNjLytOWXN4TFVNbXk3dTBSVVVON2w2SEhxNXRyMXliSkpjYVBOQTVOUlUzVE9FZzFXYkdMVDViSkoiLCJtYWMiOiIxNjhjZTcxYzlhNDdiY2VmNGQ4MjQ0OTUxNDBiODg0NWZkYzY2MzZhMmZlNjA3NDk1MDNmNWNlNmM3NjkyZGJiIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:45:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-681195059\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1799243909 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56eCVpAm7Z7Njid7pxgzHlc33GSeRPOdapi4KDAX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://osool-b2g.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>6942</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1799243909\", {\"maxDepth\":0})</script>\n"}}