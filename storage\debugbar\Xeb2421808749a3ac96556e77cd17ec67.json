{"__meta": {"id": "Xeb2421808749a3ac96556e77cd17ec67", "datetime": "2025-07-28 16:50:29", "utime": **********.45863, "method": "GET", "uri": "/dashboards/admin?_=1753710624739", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 17, "messages": [{"message": "[16:50:26] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.440008, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:26] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.513482, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:26] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.513532, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:26] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.513599, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:26] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2418", "message_html": null, "is_string": false, "label": "warning", "time": **********.640322, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:26] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2478", "message_html": null, "is_string": false, "label": "warning", "time": **********.640406, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:26] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3619", "message_html": null, "is_string": false, "label": "warning", "time": **********.640987, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:26] LOG.info: Dashboard User Type Checked ====>super_admin", "message_html": null, "is_string": false, "label": "info", "time": **********.657331, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:26] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\b77f0647bd1f084a79327132896477dc19c2b29e.php on line 465", "message_html": null, "is_string": false, "label": "warning", "time": **********.962609, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:27] LOG.warning: Optional parameter $filters declared before required parameter $userServiceProvider is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\WorkOrdersTrait.php on line 3418", "message_html": null, "is_string": false, "label": "warning", "time": **********.03936, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:27] LOG.warning: Optional parameter $serviceProviderId declared before required parameter $search is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\UserTrait.php on line 328", "message_html": null, "is_string": false, "label": "warning", "time": **********.047273, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:27] LOG.debug: Checking existence of file: uploads/profile_images/**********.png", "message_html": null, "is_string": false, "label": "debug", "time": **********.057636, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:29] LOG.debug: File exists: false", "message_html": null, "is_string": false, "label": "debug", "time": **********.1252, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:29] LOG.debug: Checking existence of file: uploads/profile_images/**********.png", "message_html": null, "is_string": false, "label": "debug", "time": **********.125485, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:29] LOG.debug: File exists: false", "message_html": null, "is_string": false, "label": "debug", "time": **********.354594, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:29] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 637", "message_html": null, "is_string": false, "label": "warning", "time": **********.374472, "xdebug_link": null, "collector": "log"}, {"message": "[16:50:29] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\f8d18d6f63e9bf36e98d3bb1b82c9e7d86b4c71c.php on line 3", "message_html": null, "is_string": false, "label": "warning", "time": **********.403806, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753710625.054873, "end": **********.458679, "duration": 4.40380597114563, "duration_str": "4.4s", "measures": [{"label": "Booting", "start": 1753710625.054873, "relative_start": 0, "end": **********.393121, "relative_end": **********.393121, "duration": 1.3382480144500732, "duration_str": "1.34s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.393133, "relative_start": 1.3382599353790283, "end": **********.458681, "relative_end": 2.1457672119140625e-06, "duration": 3.0655481815338135, "duration_str": "3.07s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 59972864, "peak_usage_str": "57MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 10, "templates": [{"name": "applications.admin.dashboard.index (\\resources\\views\\applications\\admin\\dashboard\\index.blade.php)", "param_count": 24, "params": ["data", "trans_building", "trans_buildings", "trans_the_buildings", "trans_buildings_count", "trans_the_building", "trans_building_name", "trans_it_will_appear_all_tenant_in_the_building", "trans_this_building_will_not_use_tenant_management_module", "trans_when_checked_this_building", "trans_building_tag", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.app (\\resources\\views\\layouts\\app.blade.php)", "param_count": 42, "params": ["__env", "app", "errors", "data", "trans_building", "trans_buildings", "trans_the_buildings", "trans_buildings_count", "trans_the_building", "trans_building_name", "trans_it_will_appear_all_tenant_in_the_building", "trans_this_building_will_not_use_tenant_management_module", "trans_when_checked_this_building", "trans_building_tag", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "src", "key", "percentage", "bg_status", "color", "bg_star", "users", "user_privileges", "__currentLoopData", "loop", "acwj", "contract", "property", "rwc", "ywcc"], "type": "blade"}, {"name": "layouts.partials._styles (\\resources\\views\\layouts\\partials\\_styles.blade.php)", "param_count": 42, "params": ["__env", "app", "errors", "data", "trans_building", "trans_buildings", "trans_the_buildings", "trans_buildings_count", "trans_the_building", "trans_building_name", "trans_it_will_appear_all_tenant_in_the_building", "trans_this_building_will_not_use_tenant_management_module", "trans_when_checked_this_building", "trans_building_tag", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "src", "key", "percentage", "bg_status", "color", "bg_star", "users", "user_privileges", "__currentLoopData", "loop", "acwj", "contract", "property", "rwc", "ywcc"], "type": "blade"}, {"name": "layouts.partials._header (\\resources\\views\\layouts\\partials\\_header.blade.php)", "param_count": 42, "params": ["__env", "app", "errors", "data", "trans_building", "trans_buildings", "trans_the_buildings", "trans_buildings_count", "trans_the_building", "trans_building_name", "trans_it_will_appear_all_tenant_in_the_building", "trans_this_building_will_not_use_tenant_management_module", "trans_when_checked_this_building", "trans_building_tag", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "src", "key", "percentage", "bg_status", "color", "bg_star", "users", "user_privileges", "__currentLoopData", "loop", "acwj", "contract", "property", "rwc", "ywcc"], "type": "blade"}, {"name": "layouts.partials._top_menu (\\resources\\views\\layouts\\partials\\_top_menu.blade.php)", "param_count": 42, "params": ["__env", "app", "errors", "data", "trans_building", "trans_buildings", "trans_the_buildings", "trans_buildings_count", "trans_the_building", "trans_building_name", "trans_it_will_appear_all_tenant_in_the_building", "trans_this_building_will_not_use_tenant_management_module", "trans_when_checked_this_building", "trans_building_tag", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "src", "key", "percentage", "bg_status", "color", "bg_star", "users", "user_privileges", "__currentLoopData", "loop", "acwj", "contract", "property", "rwc", "ywcc"], "type": "blade"}, {"name": "livewire.notifications.messages-notifications-list (\\resources\\views\\livewire\\notifications\\messages-notifications-list.blade.php)", "param_count": 23, "params": ["chatList", "errors", "_instance", "workspaceSlug", "totalUnreadNotifications", "previousUnreadCount", "newList", "list", "slugs", "userId", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.notifications.new-notifications-list-top-nav (\\resources\\views\\livewire\\notifications\\new-notifications-list-top-nav.blade.php)", "param_count": 28, "params": ["list", "totalUnreadNotifications", "errors", "_instance", "user", "perPage", "assignedAsset", "contractsIds", "accessBuildingsIds", "currentDate", "currentDateTime", "readyToLoad", "configOciLink", "ociLink", "selectedLanguage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.menu.aside-nav-list (\\resources\\views\\livewire\\menu\\aside-nav-list.blade.php)", "param_count": 27, "params": ["userPrivilegesAside", "user", "hasViewPrivilege", "errors", "_instance", "has<PERSON>dmin", "projectId", "project", "workOrderMenuItemColor", "flagWorkorderSidebarMenu", "userPrivileges", "closedWorkOrderCount", "maintenanceRequestCount", "vendorRegistrationApplicationRequests", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.partials._footer (\\resources\\views\\layouts\\partials\\_footer.blade.php)", "param_count": 45, "params": ["__env", "app", "errors", "data", "trans_building", "trans_buildings", "trans_the_buildings", "trans_buildings_count", "trans_the_building", "trans_building_name", "trans_it_will_appear_all_tenant_in_the_building", "trans_this_building_will_not_use_tenant_management_module", "trans_when_checked_this_building", "trans_building_tag", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "src", "key", "percentage", "bg_status", "color", "bg_star", "users", "user_privileges", "__currentLoopData", "loop", "acwj", "contract", "property", "rwc", "ywcc", "html", "url", "segments"], "type": "blade"}, {"name": "layouts.partials._scripts (\\resources\\views\\layouts\\partials\\_scripts.blade.php)", "param_count": 45, "params": ["__env", "app", "errors", "data", "trans_building", "trans_buildings", "trans_the_buildings", "trans_buildings_count", "trans_the_building", "trans_building_name", "trans_it_will_appear_all_tenant_in_the_building", "trans_this_building_will_not_use_tenant_management_module", "trans_when_checked_this_building", "trans_building_tag", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "src", "key", "percentage", "bg_status", "color", "bg_star", "users", "user_privileges", "__currentLoopData", "loop", "acwj", "contract", "property", "rwc", "ywcc", "html", "url", "segments"], "type": "blade"}]}, "route": {"uri": "GET dashboards/admin", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Admin\\DashboardControllerNew@index", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "", "where": [], "as": "admin.dashboard", "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php&line=62\">\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:62-99</a>"}, "queries": {"nb_statements": 44, "nb_failed_statements": 0, "accumulated_duration": 0.23520999999999997, "accumulated_duration_str": "235ms", "statements": [{"sql": "select * from `users` where `id` = 21 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00367, "duration_str": "3.67ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 1.56}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 191 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["191"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 1412}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 113}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 85}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.0051600000000000005, "duration_str": "5.16ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:1412", "connection": "osool_test_db", "start_percent": 1.56, "width_percent": 2.194}, {"sql": "update `users` set `allow_akaunting` = 1, `users`.`modified_at` = '2025-07-28 16:50:26' where `project_user_id` = 6942 and `user_type` not in ('osool_admin', 'super_admin') and `allow_akaunting` = 0 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2025-07-28 16:50:26", "6942", "osool_admin", "super_admin", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 1417}, {"index": 12, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 113}, {"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 85}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.03426, "duration_str": "34.26ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:1417", "connection": "osool_test_db", "start_percent": 3.754, "width_percent": 14.566}, {"sql": "select * from `users` where `users`.`id` = 21 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 1421}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 113}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 85}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00109, "duration_str": "1.09ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:1421", "connection": "osool_test_db", "start_percent": 18.32, "width_percent": 0.463}, {"sql": "select exists(select * from `service_providers` where `id` = '1' and `global_sp` = 1 and `service_providers`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["1", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 1423}, {"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 113}, {"index": 12, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 85}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.0011899999999999999, "duration_str": "1.19ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:1423", "connection": "osool_test_db", "start_percent": 18.783, "width_percent": 0.506}, {"sql": "select count(*) as aggregate from `work_orders` where `project_user_id` = 6942", "type": "query", "params": [], "bindings": ["6942"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 117}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 116}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 85}], "duration": 0.00273, "duration_str": "2.73ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:117", "connection": "osool_test_db", "start_percent": 19.289, "width_percent": 1.161}, {"sql": "select count(*) as aggregate from `work_orders` where `work_orders`.`project_user_id` = 6942 and `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and `start_date` between '2021-01-01' and '2025-07-28'", "type": "query", "params": [], "bindings": ["6942", "warranty", "4", "2021-01-01", "2025-07-28"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 481}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 137}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 136}], "duration": 0.019059999999999997, "duration_str": "19.06ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:481", "connection": "osool_test_db", "start_percent": 20.45, "width_percent": 8.103}, {"sql": "select count(*) as aggregate from `work_orders` where `work_orders`.`project_user_id` = 6942 and `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and `pass_fail` = 'pass' and `start_date` between '2021-01-01' and '2025-07-28'", "type": "query", "params": [], "bindings": ["6942", "warranty", "4", "pass", "2021-01-01", "2025-07-28"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 482}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 137}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 136}], "duration": 0.01584, "duration_str": "15.84ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:482", "connection": "osool_test_db", "start_percent": 28.553, "width_percent": 6.734}, {"sql": "select count(*) as aggregate from `work_orders` where `work_orders`.`project_user_id` = 6942 and `start_date` between '2021-01-01' and '2025-07-28' and `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and (work_orders.response_time = 'On time' OR work_orders.sp_approove = 0)", "type": "query", "params": [], "bindings": ["6942", "2021-01-01", "2025-07-28", "warranty", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 495}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 137}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 136}], "duration": 0.03467, "duration_str": "34.67ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:495", "connection": "osool_test_db", "start_percent": 35.288, "width_percent": 14.74}, {"sql": "select sum(`work_orders`.`score`) as aggregate from `work_orders` where `work_orders`.`project_user_id` = 6942 and `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and `work_orders`.`start_date` between '2021-01-01' and '2025-07-28' and `work_orders`.`bm_approove` = 1", "type": "query", "params": [], "bindings": ["6942", "warranty", "4", "2021-01-01", "2025-07-28", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 504}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 137}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 136}], "duration": 0.00926, "duration_str": "9.26ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:504", "connection": "osool_test_db", "start_percent": 50.028, "width_percent": 3.937}, {"sql": "select count(*) as aggregate from `work_orders` where `work_orders`.`project_user_id` = 6942 and `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and `work_orders`.`start_date` between '2021-01-01' and '2025-07-28' and `work_orders`.`bm_approove` = 1 and `work_orders`.`start_date` between '2021-01-01' and '2025-07-28' and `work_orders`.`bm_approove` = 1", "type": "query", "params": [], "bindings": ["6942", "warranty", "4", "2021-01-01", "2025-07-28", "1", "2021-01-01", "2025-07-28", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 509}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 137}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 136}], "duration": 0.01014, "duration_str": "10.14ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:509", "connection": "osool_test_db", "start_percent": 53.965, "width_percent": 4.311}, {"sql": "select avg(`work_orders`.`score`) as aggregate from `work_orders` where `work_orders`.`project_user_id` = 6942 and `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and `work_orders`.`start_date` between '2021-01-01' and '2025-07-28' and `work_orders`.`bm_approove` = 1 and `work_orders`.`start_date` between '2021-01-01' and '2025-07-28' and `work_orders`.`bm_approove` = 1 and `work_orders`.`start_date` between '2021-01-01' and '2025-07-28' and `work_orders`.`bm_approove` = 1", "type": "query", "params": [], "bindings": ["6942", "warranty", "4", "2021-01-01", "2025-07-28", "1", "2021-01-01", "2025-07-28", "1", "2021-01-01", "2025-07-28", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 514}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 137}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 136}], "duration": 0.016800000000000002, "duration_str": "16.8ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:514", "connection": "osool_test_db", "start_percent": 58.276, "width_percent": 7.143}, {"sql": "select `id`, work_orders.id, COUNT(work_orders.id) as total,\nSUM(CASE WHEN work_orders.work_order_type = \"reactive\" THEN 1 ELSE 0 END) AS reactive_count,\nSUM(CASE WHEN work_orders.work_order_type = \"preventive\" THEN 1 ELSE 0 END) AS preventive_count from `work_orders` where `work_orders`.`project_user_id` = 6942 and `work_orders`.`start_date` <= '2025-07-28' and `work_orders`.`is_deleted` = 'no' limit 1", "type": "query", "params": [], "bindings": ["6942", "2025-07-28", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 529}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 137}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 136}], "duration": 0.00275, "duration_str": "2.75ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:529", "connection": "osool_test_db", "start_percent": 65.418, "width_percent": 1.169}, {"sql": "select `id`, rating, COUNT(*) as count from `work_orders` where `work_orders`.`project_user_id` = 6942 and `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and `start_date` between '2021-01-01' and '2025-07-28' group by `rating`", "type": "query", "params": [], "bindings": ["6942", "warranty", "4", "2021-01-01", "2025-07-28"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 559}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 137}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 136}], "duration": 0.014960000000000001, "duration_str": "14.96ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:559", "connection": "osool_test_db", "start_percent": 66.587, "width_percent": 6.36}, {"sql": "select `id`, SUM(rating) as total_stars, COUNT(rating) as ratings, SUM(CASE WHEN work_orders.rating > 0 THEN 1 ELSE 0 END) AS rated_job_count from `work_orders` where `work_orders`.`project_user_id` = 6942 and `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and `start_date` between '2021-01-01' and '2025-07-28' limit 1", "type": "query", "params": [], "bindings": ["6942", "warranty", "4", "2021-01-01", "2025-07-28"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 566}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 137}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 136}], "duration": 0.01644, "duration_str": "16.44ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:566", "connection": "osool_test_db", "start_percent": 72.948, "width_percent": 6.989}, {"sql": "select count(*) as aggregate from `work_orders` where `work_orders`.`project_user_id` = 6942 and `start_date` between '2021-01-01' and '2025-07-28' and `work_orders`.`contract_type` != 'warranty'", "type": "query", "params": [], "bindings": ["6942", "2021-01-01", "2025-07-28", "warranty"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 645}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 137}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 136}], "duration": 0.00188, "duration_str": "1.88ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:645", "connection": "osool_test_db", "start_percent": 79.937, "width_percent": 0.799}, {"sql": "select * from `asset_categories` where `is_deleted` = 'no' and `user_id` = 6942 and `asset_categories`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["no", "6942"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 650}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 137}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 136}], "duration": 0.00163, "duration_str": "1.63ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:650", "connection": "osool_test_db", "start_percent": 80.736, "width_percent": 0.693}, {"sql": "select `work_orders`.`asset_category_id`, count(*) as count from `work_orders` where `work_orders`.`project_user_id` = 6942 and `start_date` between '2021-01-01' and '2025-07-28' and `work_orders`.`contract_type` != 'warranty' and `work_orders`.`asset_category_id` in (1121) group by `work_orders`.`asset_category_id`", "type": "query", "params": [], "bindings": ["6942", "2021-01-01", "2025-07-28", "warranty", "1121"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 660}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 137}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 136}], "duration": 0.00328, "duration_str": "3.28ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:660", "connection": "osool_test_db", "start_percent": 81.429, "width_percent": 1.394}, {"sql": "select `id`, `contract_number`, `start_date`, `end_date`, DATEDIFF(end_date, start_date) / 365 as count from `contracts` where `user_id` = 6942 and `contracts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6942"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 815}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 761}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 141}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}], "duration": 0.00166, "duration_str": "1.66ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:815", "connection": "osool_test_db", "start_percent": 82.824, "width_percent": 0.706}, {"sql": "select `id`, `contract_number`, `start_date`, `end_date`, DATEDIFF(end_date, start_date) / 365 as count, YEAR(start_date) as year, COUNT(id) as count from `contracts` where `is_deleted` = 'no' and `deleted_at` is null and (YEAR(start_date) >= '2025' and YEAR(start_date) <= 2032) and `user_id` = 6942 and `contracts`.`deleted_at` is null group by `year`", "type": "query", "params": [], "bindings": ["no", "2025", "2032", "6942"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 838}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 761}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 141}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}], "duration": 0.0024100000000000002, "duration_str": "2.41ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:838", "connection": "osool_test_db", "start_percent": 83.53, "width_percent": 1.025}, {"sql": "select count(*) as aggregate from `contracts` where `user_id` = 6942 and `is_deleted` = 'no' and `deleted_at` is null and `contracts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6942", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 769}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 141}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 140}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:769", "connection": "osool_test_db", "start_percent": 84.554, "width_percent": 0.349}, {"sql": "select `id`, `contract_number`, `start_date`, `end_date`, DATEDIFF(end_date, start_date) / 365 as count from `contracts` where `user_id` = 6942 and `is_deleted` = 'no' and `deleted_at` is null and `is_deleted` = 'no' and `deleted_at` is null and `contracts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6942", "no", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 774}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 141}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 140}], "duration": 0.00095, "duration_str": "950μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:774", "connection": "osool_test_db", "start_percent": 84.903, "width_percent": 0.404}, {"sql": "select sum(`contracts`.`contract_value`) as aggregate from `contracts` where `user_id` = 6942 and `is_deleted` = 'no' and `deleted_at` is null and `is_deleted` = 'no' and `deleted_at` is null and `is_deleted` = 'no' and `deleted_at` is null and `contracts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6942", "no", "no", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 779}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 141}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 140}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:779", "connection": "osool_test_db", "start_percent": 85.307, "width_percent": 0.306}, {"sql": "select `contracts`.`start_date`, `contracts`.`end_date` from `contracts` inner join `service_providers` on `contracts`.`service_provider_id` = `service_providers`.`id` where `contracts`.`is_deleted` = 'no' and `contracts`.`deleted_at` is null order by `contracts`.`start_date` asc, `contracts`.`end_date` desc limit 1", "type": "query", "params": [], "bindings": ["no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 868}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 782}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 141}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}], "duration": 0.00115, "duration_str": "1.15ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:868", "connection": "osool_test_db", "start_percent": 85.613, "width_percent": 0.489}, {"sql": "select count(*) as aggregate from `contracts` where `user_id` = 6942 and `is_deleted` = 'no' and `deleted_at` is null and `is_deleted` = 'no' and `deleted_at` is null and `is_deleted` = 'no' and `deleted_at` is null and contracts.end_date <= date_add(now(), interval 6 month) and `contracts`.`deleted_at` is null and `contracts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6942", "no", "no", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 920}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 785}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 141}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}], "duration": 0.00085, "duration_str": "850μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:920", "connection": "osool_test_db", "start_percent": 86.102, "width_percent": 0.361}, {"sql": "select count(*) as aggregate from `contracts` where `user_id` = 6942 and `is_deleted` = 'no' and `deleted_at` is null and `is_deleted` = 'no' and `deleted_at` is null and `is_deleted` = 'no' and `deleted_at` is null and contracts.end_date >= date_add(now(), interval 12 month) and `contracts`.`deleted_at` is null and `contracts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6942", "no", "no", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 926}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 785}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 141}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}], "duration": 0.00139, "duration_str": "1.39ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:926", "connection": "osool_test_db", "start_percent": 86.463, "width_percent": 0.591}, {"sql": "select count(*) as aggregate from `contracts` where `user_id` = 6942 and `is_deleted` = 'no' and `deleted_at` is null and `is_deleted` = 'no' and `deleted_at` is null and `is_deleted` = 'no' and `deleted_at` is null and contracts.end_date >= date_add(now(), interval 6 month) and contracts.end_date <= date_add(now(), interval 12 month) and `contracts`.`deleted_at` is null and `contracts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6942", "no", "no", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 933}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 785}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 141}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}], "duration": 0.0008399999999999999, "duration_str": "840μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:933", "connection": "osool_test_db", "start_percent": 87.054, "width_percent": 0.357}, {"sql": "select SUM(buildings_count) AS total_buildings, COUNT(CASE WHEN property_type = \"complex\" THEN 1 END) AS total_complexes from `properties` where `user_id` = 6942 and `deleted_at` is null and `is_deleted` = 'no' and `properties`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6942", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 706}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 145}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 144}], "duration": 0.00123, "duration_str": "1.23ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:706", "connection": "osool_test_db", "start_percent": 87.411, "width_percent": 0.523}, {"sql": "select `beneficiary`.*, (select count(*) from `property_buildings` where `beneficiary`.`id` = `property_buildings`.`beneficiaries` and `property_buildings`.`deleted_at` is null) as `property_buildings_count` from `beneficiary` where `user_id` = 6942 and `is_deleted` = 'no' and `beneficiary`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6942", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 730}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 149}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 148}], "duration": 0.00158, "duration_str": "1.58ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:730", "connection": "osool_test_db", "start_percent": 87.934, "width_percent": 0.672}, {"sql": "select `property_type`, `buildings_count`, `properties`.`latitude`, `properties`.`longitude`, `properties`.`location`, `property_buildings`.`latitude` as `latitude_complex`, `property_buildings`.`longitude` as `longitude_complex`, `property_buildings`.`location` as `location_complex`, `properties`.`location_type`, `property_tag`, `properties`.`id`, `complex_name`, `property_buildings`.`building_name`, `property_buildings`.`id` as `building_id` from `properties` inner join `property_buildings` on `property_buildings`.`property_id` = `properties`.`id` where `properties`.`is_deleted` = 'no' and `properties`.`user_id` = 6942 and `property_buildings`.`is_deleted` = 'no' and `property_buildings`.`deleted_at` is null and `properties`.`deleted_at` is null and `properties`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["no", "6942", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 983}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 153}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 152}], "duration": 0.00767, "duration_str": "7.67ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:983", "connection": "osool_test_db", "start_percent": 88.606, "width_percent": 3.261}, {"sql": "select `pass_fail_target`, `target_response_rate` from `project_settings` where `project_id` = '191' limit 1", "type": "query", "params": [], "bindings": ["191"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 3495}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 161}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 160}], "duration": 0.0009599999999999999, "duration_str": "960μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:3495", "connection": "osool_test_db", "start_percent": 91.867, "width_percent": 0.408}, {"sql": "select * from `client_industry` where `client_industry`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 4198}, {"index": 20, "namespace": null, "name": "\\app\\Providers\\AppServiceProvider.php", "line": 53}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.00109, "duration_str": "1.09ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:4198", "connection": "osool_test_db", "start_percent": 92.275, "width_percent": 0.463}, {"sql": "select * from `release_notes` where `store_status` = 1 and `release_notes`.`deleted_at` is null group by `version`", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 3048}, {"index": 15, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 51}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:3048", "connection": "osool_test_db", "start_percent": 92.738, "width_percent": 0.421}, {"sql": "select * from `crm_user` where `crm_user`.`user_id` = 21 and `crm_user`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "9740534860d5c11a9cf9e73e8939e4083f82ba1d", "line": 122}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 122}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "view::9740534860d5c11a9cf9e73e8939e4083f82ba1d:122", "connection": "osool_test_db", "start_percent": 93.159, "width_percent": 0.4}, {"sql": "select `name`, `name_ar` from `user_type` where `slug` = 'super_admin' limit 1", "type": "query", "params": [], "bindings": ["super_admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1929}, {"index": 14, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 161}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.0011899999999999999, "duration_str": "1.19ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1929", "connection": "osool_test_db", "start_percent": 93.559, "width_percent": 0.506}, {"sql": "select `project_name`, `project_name_ar` from `projects_details` where `id` = '191' limit 1", "type": "query", "params": [], "bindings": ["191"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 959}, {"index": 14, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 181}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:959", "connection": "osool_test_db", "start_percent": 94.065, "width_percent": 0.23}, {"sql": "select `id`, `project_image`, `use_beneficiary_module`, `use_tenant_module`, `benificiary_status`, `tenant_status`, `project_name`, `project_name_ar`, `use_crm_module` from `projects_details` where `id` = '191' and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["191"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\ProjectDetailTrait.php", "line": 11}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 128}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 71}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "\\app\\Http\\Traits\\ProjectDetailTrait.php:11", "connection": "osool_test_db", "start_percent": 94.294, "width_percent": 0.268}, {"sql": "select `id`, `created_at` from `users` where `project_id` = '191' and `user_type` = 'admin' and `status` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["191", "admin", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\UserTrait.php", "line": 256}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Traits\\UserTrait.php", "line": 267}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 158}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 74}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.004849999999999999, "duration_str": "4.85ms", "stmt_id": "\\app\\Http\\Traits\\UserTrait.php:256", "connection": "osool_test_db", "start_percent": 94.562, "width_percent": 2.062}, {"sql": "select count(*) as aggregate from `vendor_profiles` where `submit_status` = 'submit' and `application_status` = 'no_action' and `vendor_profiles`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["submit", "no_action"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 220}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 60}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php:220", "connection": "osool_test_db", "start_percent": 96.624, "width_percent": 0.391}, {"sql": "select exists(select * from `projects_details` where `id` = 191 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["191", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 897}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00121, "duration_str": "1.21ms", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_test_db", "start_percent": 97.015, "width_percent": 0.514}, {"sql": "select exists(select * from `crm_user` where `user_id` = 21) as `exists`", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 897}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.0007700000000000001, "duration_str": "770μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_test_db", "start_percent": 97.53, "width_percent": 0.327}, {"sql": "select `id` from `users` where `project_id` = '191' and `user_type` = 'admin' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["191", "admin", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 928}, {"index": 14, "namespace": "view", "name": "f8d18d6f63e9bf36e98d3bb1b82c9e7d86b4c71c", "line": 9}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00344, "duration_str": "3.44ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:928", "connection": "osool_test_db", "start_percent": 97.857, "width_percent": 1.463}, {"sql": "select exists(select * from `projects_details` where `id` = 191 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["191", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 183}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.0010500000000000002, "duration_str": "1.05ms", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_test_db", "start_percent": 99.32, "width_percent": 0.446}, {"sql": "select exists(select * from `crm_user` where `user_id` = 21) as `exists`", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 183}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_test_db", "start_percent": 99.766, "width_percent": 0.234}]}, "models": {"data": {"App\\Models\\ReleaseNotes": 2, "App\\Models\\ClientIndustry": 1, "App\\Models\\ProjectSettings": 1, "App\\Models\\Property": 2, "App\\Models\\Contracts": 5, "App\\Models\\AssetCategory": 1, "App\\Models\\WorkOrders": 2, "App\\Models\\ProjectsDetails": 2, "App\\Models\\User": 3}, "count": 19}, "livewire": {"data": {"notifications.messages-notifications-list #kWokRphkS2Ii1sxc6IPG": "array:5 [\n  \"data\" => array:7 [\n    \"workspaceSlug\" => \"memo\"\n    \"totalUnreadNotifications\" => 0\n    \"previousUnreadCount\" => 0\n    \"newList\" => null\n    \"list\" => []\n    \"slugs\" => array:3 [\n      0 => \"facebook\"\n      1 => \"whatsapp\"\n      2 => \"instagram\"\n    ]\n    \"userId\" => null\n  ]\n  \"name\" => \"notifications.messages-notifications-list\"\n  \"view\" => \"livewire.notifications.messages-notifications-list\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\MessagesNotificationsList\"\n  \"id\" => \"kWokRphkS2Ii1sxc6IPG\"\n]", "notifications.new-notifications-list-top-nav #8SpcDK0FEikM6YM5SLF3": "array:5 [\n  \"data\" => array:11 [\n    \"user\" => null\n    \"perPage\" => null\n    \"assignedAsset\" => null\n    \"contractsIds\" => null\n    \"accessBuildingsIds\" => null\n    \"currentDate\" => null\n    \"currentDateTime\" => null\n    \"readyToLoad\" => null\n    \"configOciLink\" => null\n    \"ociLink\" => null\n    \"selectedLanguage\" => null\n  ]\n  \"name\" => \"notifications.new-notifications-list-top-nav\"\n  \"view\" => \"livewire.notifications.new-notifications-list-top-nav\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav\"\n  \"id\" => \"8SpcDK0FEikM6YM5SLF3\"\n]", "menu.aside-nav-list #": "array:7 [\n  \"data\" => array:10 [\n    \"user\" => App\\Models\\User {#3867\n      #connection: \"mysql\"\n      #table: \"users\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:78 [\n        \"id\" => 21\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$JCvJmAzGnnOeoh9po8kYju4db9GhmC.s6.ue6cmFhafhv6MgdUINu\"\n        \"name\" => \"Super Admin\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => \"123456789\"\n        \"profile_img\" => \"**********.png\"\n        \"emp_id\" => \"1234112\"\n        \"profession_id\" => null\n        \"emp_dept\" => \"Management\"\n        \"building_ids\" => null\n        \"contract_ids\" => \"\"\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => \"\"\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => \"1,2,3,4\"\n        \"role_cities\" => \"1,2,3,4\"\n        \"asset_categories\" => \"1,2,3,4\"\n        \"keeper_warehouses\" => null\n        \"properties\" => \"1\"\n        \"contracts\" => \"1\"\n        \"beneficiary\" => \"1\"\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"super_admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 0\n        \"project_id\" => 191\n        \"project_user_id\" => 6942\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2022-03-03 13:52:49\"\n        \"modified_at\" => \"2025-07-28 16:50:21\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"memo\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNzEwNTU2LCJleHAiOjE3NTM3MTQxNTYsIm5iZiI6MTc1MzcxMDU1NiwianRpIjoiS1RSR3dtTXd5cGszNDhzTCIsInN1YiI6IjEiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.F-HytyuBuf52RxpgRuHKyq9INjqBN5U6c4lL2F7q6fI\"\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #original: array:78 [\n        \"id\" => 21\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$JCvJmAzGnnOeoh9po8kYju4db9GhmC.s6.ue6cmFhafhv6MgdUINu\"\n        \"name\" => \"Super Admin\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => \"123456789\"\n        \"profile_img\" => \"**********.png\"\n        \"emp_id\" => \"1234112\"\n        \"profession_id\" => null\n        \"emp_dept\" => \"Management\"\n        \"building_ids\" => null\n        \"contract_ids\" => \"\"\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => \"\"\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => \"1,2,3,4\"\n        \"role_cities\" => \"1,2,3,4\"\n        \"asset_categories\" => \"1,2,3,4\"\n        \"keeper_warehouses\" => null\n        \"properties\" => \"1\"\n        \"contracts\" => \"1\"\n        \"beneficiary\" => \"1\"\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"super_admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 0\n        \"project_id\" => 191\n        \"project_user_id\" => 6942\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2022-03-03 13:52:49\"\n        \"modified_at\" => \"2025-07-28 16:50:21\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"memo\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNzEwNTU2LCJleHAiOjE3NTM3MTQxNTYsIm5iZiI6MTc1MzcxMDU1NiwianRpIjoiS1RSR3dtTXd5cGszNDhzTCIsInN1YiI6IjEiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.F-HytyuBuf52RxpgRuHKyq9INjqBN5U6c4lL2F7q6fI\"\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #changes: []\n      #casts: array:2 [\n        \"email_verified_at\" => \"datetime\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:2 [\n        \"projectDetails\" => App\\Models\\ProjectsDetails {#3886\n          #connection: \"mysql\"\n          #table: \"projects_details\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:23 [\n            \"id\" => 191\n            \"user_id\" => 7034\n            \"project_name\" => \"Testing\"\n            \"project_name_ar\" => \"Testing23\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => \"20250427115740146654.jpg\"\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2025-02-18 17:58:25\"\n            \"updated_at\" => \"2025-07-28 15:47:26\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 0\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 0\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => null\n            \"contract_end_date\" => null\n            \"share_post\" => 1\n            \"deleted_at\" => null\n            \"crm_workspace_slug\" => \"testing23\"\n          ]\n          #original: array:23 [\n            \"id\" => 191\n            \"user_id\" => 7034\n            \"project_name\" => \"Testing\"\n            \"project_name_ar\" => \"Testing23\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => \"20250427115740146654.jpg\"\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2025-02-18 17:58:25\"\n            \"updated_at\" => \"2025-07-28 15:47:26\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 0\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 0\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => null\n            \"contract_end_date\" => null\n            \"share_post\" => 1\n            \"deleted_at\" => null\n            \"crm_workspace_slug\" => \"testing23\"\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:16 [\n            0 => \"user_id\"\n            1 => \"project_name\"\n            2 => \"project_name_ar\"\n            3 => \"project_image\"\n            4 => \"industry_type\"\n            5 => \"created_by\"\n            6 => \"is_deleted\"\n            7 => \"use_erp_module\"\n            8 => \"use_tenant_module\"\n            9 => \"tenant_status\"\n            10 => \"use_beneficiary_module\"\n            11 => \"benificiary_status\"\n            12 => \"community_status\"\n            13 => \"contract_status\"\n            14 => \"share_post\"\n            15 => \"use_crm_module\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n          #excludedAttributes: []\n          +auditEvent: null\n          +auditCustomOld: null\n          +auditCustomNew: null\n          +isCustomEvent: false\n          +preloadedResolverData: []\n        }\n        \"crmUser\" => null\n      ]\n      #touches: []\n      +timestamps: true\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: array:62 [\n        0 => \"allow_akaunting\"\n        1 => \"email\"\n        2 => \"password\"\n        3 => \"name\"\n        4 => \"first_name\"\n        5 => \"last_name\"\n        6 => \"apartment\"\n        7 => \"unit_receival_date\"\n        8 => \"later_booking_alert\"\n        9 => \"phone\"\n        10 => \"profile_img\"\n        11 => \"address\"\n        12 => \"country_id\"\n        13 => \"city_id\"\n        14 => \"role_regions\"\n        15 => \"role_cities\"\n        16 => \"asset_categories\"\n        17 => \"properties\"\n        18 => \"contracts\"\n        19 => \"beneficiary\"\n        20 => \"service_provider\"\n        21 => \"user_type\"\n        22 => \"project_id\"\n        23 => \"project_user_id\"\n        24 => \"created_by\"\n        25 => \"status\"\n        26 => \"user_privileges\"\n        27 => \"approved_max_amount\"\n        28 => \"emp_id\"\n        29 => \"profession_id\"\n        30 => \"emp_dept\"\n        31 => \"building_ids\"\n        32 => \"contract_ids\"\n        33 => \"supervisor_id\"\n        34 => \"sp_admin_id\"\n        35 => \"langForSms\"\n        36 => \"deleted_at\"\n        37 => \"otp\"\n        38 => \"temp_password\"\n        39 => \"otp_for_password\"\n        40 => \"otp_for_password_verified\"\n        41 => \"temp_phone_number\"\n        42 => \"favorite_language\"\n        43 => \"is_subcontractors_worker\"\n        44 => \"keeper_warehouses\"\n        45 => \"save_later_date\"\n        46 => \"first_login\"\n        47 => \"is_unit_link\"\n        48 => \"akaunting_vendor_id\"\n        49 => \"akaunting_customer_id\"\n        50 => \"crm_api_token\"\n        51 => \"workspace_slug\"\n        52 => \"is_bma_area_manager\"\n        53 => \"assigned_workers\"\n        54 => \"sleep_mode\"\n        55 => \"offline_mode\"\n        56 => \"attendance_mandatory\"\n        57 => \"admin_level\"\n        58 => \"role\"\n        59 => \"attendance_target\"\n        60 => \"salary\"\n        61 => \"show_extra_info\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #rememberTokenName: \"remember_token\"\n      #accessToken: null\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n    }\n    \"hasAdmin\" => 6942\n    \"projectId\" => \"191\"\n    \"project\" => App\\Models\\ProjectsDetails {#4806\n      #connection: \"mysql\"\n      #table: \"projects_details\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:9 [\n        \"id\" => 191\n        \"project_image\" => \"20250427115740146654.jpg\"\n        \"use_beneficiary_module\" => 0\n        \"use_tenant_module\" => 0\n        \"benificiary_status\" => 1\n        \"tenant_status\" => 1\n        \"project_name\" => \"Testing\"\n        \"project_name_ar\" => \"Testing23\"\n        \"use_crm_module\" => 1\n      ]\n      #original: array:9 [\n        \"id\" => 191\n        \"project_image\" => \"20250427115740146654.jpg\"\n        \"use_beneficiary_module\" => 0\n        \"use_tenant_module\" => 0\n        \"benificiary_status\" => 1\n        \"tenant_status\" => 1\n        \"project_name\" => \"Testing\"\n        \"project_name_ar\" => \"Testing23\"\n        \"use_crm_module\" => 1\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:16 [\n        0 => \"user_id\"\n        1 => \"project_name\"\n        2 => \"project_name_ar\"\n        3 => \"project_image\"\n        4 => \"industry_type\"\n        5 => \"created_by\"\n        6 => \"is_deleted\"\n        7 => \"use_erp_module\"\n        8 => \"use_tenant_module\"\n        9 => \"tenant_status\"\n        10 => \"use_beneficiary_module\"\n        11 => \"benificiary_status\"\n        12 => \"community_status\"\n        13 => \"contract_status\"\n        14 => \"share_post\"\n        15 => \"use_crm_module\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n    }\n    \"workOrderMenuItemColor\" => null\n    \"flagWorkorderSidebarMenu\" => false\n    \"userPrivileges\" => null\n    \"closedWorkOrderCount\" => null\n    \"maintenanceRequestCount\" => 0\n    \"vendorRegistrationApplicationRequests\" => 2\n  ]\n  \"oldData\" => null\n  \"actionQueue\" => null\n  \"name\" => \"menu.aside-nav-list\"\n  \"view\" => \"livewire.menu.aside-nav-list\"\n  \"component\" => \"App\\Http\\Livewire\\Menu\\AsideNavList\"\n  \"id\" => null\n]"}, "count": 3}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"locale": "en", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/dashboards/admin\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "21", "plain_user_password": "Tarqeem21", "entered_project_id": "191", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/dashboards/admin", "status_code": "<pre class=sf-dump id=sf-dump-1822382084 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1822382084\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-683519674 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1753710624739</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-683519674\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1817020130 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1753710624739</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1817020130\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1118520114 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://osool-b2g.test/dashboards/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IlF4azY1cXU5Y0dwSnA4MUhFWk4wSEE9PSIsInZhbHVlIjoiYytTa3NWOWFKbDJMUW9sUkRxdXpBMkNoWHdld003MjNtRFp0QzJPd0lRdFFqM0dORnpsU0x6MUtTb2R1d1p5N3FFcUdGU1JBRHdpVnFZNzRIWHE4SmV0YTZUc3JvaEFTSGJHRVBKYUloVTJvdVU2M0JlK3FmM0sxZ2tBenVBT24iLCJtYWMiOiIxMjMzOGIxY2E3NjRkNTUyNmZmYjcwNDhhYzMwZGNjMWUzN2RjODVhNmRlNjI1OTk0OTY2NTA3Mzk5ZDYwYjg0IiwidGFnIjoiIn0%3D; osool_session=XzOD0ToWvNGBHiqr85ElEoWZirExjOyR4t8uXK8F</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1118520114\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1578697504 data-indent-pad=\"  \"><span class=sf-dump-note>array:40</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://osool-b2g.test/dashboards/admin</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IlF4azY1cXU5Y0dwSnA4MUhFWk4wSEE9PSIsInZhbHVlIjoiYytTa3NWOWFKbDJMUW9sUkRxdXpBMkNoWHdld003MjNtRFp0QzJPd0lRdFFqM0dORnpsU0x6MUtTb2R1d1p5N3FFcUdGU1JBRHdpVnFZNzRIWHE4SmV0YTZUc3JvaEFTSGJHRVBKYUloVTJvdVU2M0JlK3FmM0sxZ2tBenVBT24iLCJtYWMiOiIxMjMzOGIxY2E3NjRkNTUyNmZmYjcwNDhhYzMwZGNjMWUzN2RjODVhNmRlNjI1OTk0OTY2NTA3Mzk5ZDYwYjg0IiwidGFnIjoiIn0%3D; osool_session=XzOD0ToWvNGBHiqr85ElEoWZirExjOyR4t8uXK8F</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52513</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/dashboards/admin</span>\"\n  \"<span class=sf-dump-key>REDIRECT_QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"15 characters\">_=1753710624739</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"15 characters\">_=1753710624739</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"33 characters\">/dashboards/admin?_=1753710624739</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753710625.0549</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753710625</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1578697504\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-988043242 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-988043242\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1314585034 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 13:50:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImNyY2NRQkYrRk05bDBaMlZ2cDBvWEE9PSIsInZhbHVlIjoiNTAyOW5IbG9EOWFZMHZxZnQ2amYxSXRnRmRveGVxU3RVWDRsbHFvaGZoK3VLc0NLTkY4RG9XejVpUCtIbUJIazN5ZmxJZ1ZXZ0JydXRTbXo0eldYVExsOFFEZ21FUmZvNTRHaVZDUmxJdXI0TXRSWGVETFhTWUtYV2NEN3Zza20iLCJtYWMiOiIxODA2NDhjNDkyYThmNjkyYjI1ZThhYmY0ZWUzNWE2ZTA0NTdhMmU4NjM4NjZlMDYxYTZlMzVhZmZhOGYxYjQzIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:50:29 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IjZhK0NQUVRnZUFCUUJtRnBYUE1DbGc9PSIsInZhbHVlIjoiSHljcWVnVEh2RC9EbjAwSHVVUndRazdWN29ocU1tU1FzS25zZU5yNi90amh3emE1WjNwclpDQ1NYTkZWdXlkaENpTjhXNTFycXBBdGVSTlEvOEFWOGx0VWxGT0s1M1lGc2hHaUh6RFBxbjh4dEx4SVNqcmY2cUFUYTZtMlE5NkciLCJtYWMiOiI3MzBlN2I1OWM2NjMwZmVjYzIxMGJhZTNlMTZmMTIxZTFhMTMzMDVmNGIxMDA1ZTk1M2U2OTRkNTI1MTdlNTdmIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:50:29 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImNyY2NRQkYrRk05bDBaMlZ2cDBvWEE9PSIsInZhbHVlIjoiNTAyOW5IbG9EOWFZMHZxZnQ2amYxSXRnRmRveGVxU3RVWDRsbHFvaGZoK3VLc0NLTkY4RG9XejVpUCtIbUJIazN5ZmxJZ1ZXZ0JydXRTbXo0eldYVExsOFFEZ21FUmZvNTRHaVZDUmxJdXI0TXRSWGVETFhTWUtYV2NEN3Zza20iLCJtYWMiOiIxODA2NDhjNDkyYThmNjkyYjI1ZThhYmY0ZWUzNWE2ZTA0NTdhMmU4NjM4NjZlMDYxYTZlMzVhZmZhOGYxYjQzIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:50:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IjZhK0NQUVRnZUFCUUJtRnBYUE1DbGc9PSIsInZhbHVlIjoiSHljcWVnVEh2RC9EbjAwSHVVUndRazdWN29ocU1tU1FzS25zZU5yNi90amh3emE1WjNwclpDQ1NYTkZWdXlkaENpTjhXNTFycXBBdGVSTlEvOEFWOGx0VWxGT0s1M1lGc2hHaUh6RFBxbjh4dEx4SVNqcmY2cUFUYTZtMlE5NkciLCJtYWMiOiI3MzBlN2I1OWM2NjMwZmVjYzIxMGJhZTNlMTZmMTIxZTFhMTMzMDVmNGIxMDA1ZTk1M2U2OTRkNTI1MTdlNTdmIiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:50:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1314585034\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1175241600 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NJYoACaVNgxuSNNSORBCyLVq5ss784DybrJHlY7Y</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://osool-b2g.test/dashboards/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>21</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Tarqeem21</span>\"\n  \"<span class=sf-dump-key>entered_project_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">191</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1175241600\", {\"maxDepth\":0})</script>\n"}}