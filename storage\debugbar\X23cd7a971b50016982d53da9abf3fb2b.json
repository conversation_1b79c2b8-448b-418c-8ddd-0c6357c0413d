{"__meta": {"id": "X23cd7a971b50016982d53da9abf3fb2b", "datetime": "2025-07-28 16:45:32", "utime": 1753710332.446831, "method": "POST", "uri": "/logout", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[16:45:32] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753710332.283066, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753710331.737574, "end": 1753710332.446852, "duration": 0.709277868270874, "duration_str": "709ms", "measures": [{"label": "Booting", "start": 1753710331.737574, "relative_start": 0, "end": 1753710332.257895, "relative_end": 1753710332.257895, "duration": 0.5203208923339844, "duration_str": "520ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753710332.25791, "relative_start": 0.5203359127044678, "end": 1753710332.446853, "relative_end": 9.5367431640625e-07, "duration": 0.18894290924072266, "duration_str": "189ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 37069176, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST logout", "middleware": "web", "controller": "App\\Http\\Controllers\\Auth\\LoginController@logout", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "logout", "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Auth\\LoginController.php&line=135\">\\app\\Http\\Controllers\\Auth\\LoginController.php:135-158</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.022010000000000002, "accumulated_duration_str": "22.01ms", "statements": [{"sql": "select * from `users` where `id` = 21 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 23}], "duration": 0.00683, "duration_str": "6.83ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 31.031}, {"sql": "update `users` set `project_id` = 0, `project_user_id` = 0, `users`.`modified_at` = '2025-07-28 16:45:32' where `id` = 21 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["0", "0", "2025-07-28 16:45:32", "21"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 141}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00558, "duration_str": "5.58ms", "stmt_id": "\\app\\Http\\Controllers\\Auth\\LoginController.php:141", "connection": "osool_test_db", "start_percent": 31.031, "width_percent": 25.352}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 0 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\User.php", "line": 209}, {"index": 21, "namespace": null, "name": "\\app\\Models\\User.php", "line": 200}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 147}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Models\\User.php:209", "connection": "osool_test_db", "start_percent": 56.383, "width_percent": 3.271}, {"sql": "select `projects_details`.*, `user_projects`.`user_id` as `pivot_user_id`, `user_projects`.`project_id` as `pivot_project_id` from `projects_details` inner join `user_projects` on `projects_details`.`id` = `user_projects`.`project_id` where `user_projects`.`user_id` = 21 and `projects_details`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\User.php", "line": 212}, {"index": 20, "namespace": null, "name": "\\app\\Models\\User.php", "line": 200}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 147}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00212, "duration_str": "2.12ms", "stmt_id": "\\app\\Models\\User.php:212", "connection": "osool_test_db", "start_percent": 59.655, "width_percent": 9.632}, {"sql": "update `users` set `allow_akaunting` = 0, `users`.`modified_at` = '2025-07-28 16:45:32' where `id` = 21", "type": "query", "params": [], "bindings": ["0", "2025-07-28 16:45:32", "21"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 147}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00199, "duration_str": "1.99ms", "stmt_id": "\\app\\Http\\Controllers\\Auth\\LoginController.php:147", "connection": "osool_test_db", "start_percent": 69.287, "width_percent": 9.041}, {"sql": "insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('{\\\"allow_akaunting\\\":1}', '{\\\"allow_akaunting\\\":0}', 'updated', 21, 'App\\Models\\User', 21, 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'http://osool-b2g.test/logout', '2025-07-28 16:45:32', '2025-07-28 16:45:32')", "type": "query", "params": [], "bindings": ["{&quot;allow_akaunting&quot;:1}", "{&quot;allow_akaunting&quot;:0}", "updated", "21", "App\\Models\\User", "21", "App\\Models\\User", "", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "http://osool-b2g.test/logout", "2025-07-28 16:45:32", "2025-07-28 16:45:32"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "\\vendor\\owen-it\\laravel-auditing\\src\\Auditor.php", "line": 83}, {"index": 25, "namespace": null, "name": "\\vendor\\owen-it\\laravel-auditing\\src\\AuditableObserver.php", "line": 99}, {"index": 26, "namespace": null, "name": "\\vendor\\owen-it\\laravel-auditing\\src\\AuditableObserver.php", "line": 49}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 147}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00477, "duration_str": "4.77ms", "stmt_id": "\\vendor\\owen-it\\laravel-auditing\\src\\Auditor.php:83", "connection": "osool_test_db", "start_percent": 78.328, "width_percent": 21.672}]}, "models": {"data": {"App\\Models\\User": 1}, "count": 1}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"locale": "en", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "56eCVpAm7Z7Njid7pxgzHlc33GSeRPOdapi4KDAX", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/logout", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1713096048 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MoHVz4Tx7nVo7GO6J30zRNXQMrc7mCwjsdZxDGeF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1713096048\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-205563074 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/workspace/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6ImFLL0hEVGdIZlJEVlU3Rm1RSThKMWc9PSIsInZhbHVlIjoiTXk1SUZqeTdvWC84UEhBOEs4d1Nxa3RjSkhlOWdudkt2VDJ2dGtkU1BrS3BUaWRMM3VZRElBV1M5QkYzcEJxQWVCMTM2RVViSFcyU1ZIT1FuRzA1Y1ArVTdOZi80ZlE1bFFrOUllak1kS0JxalIyVGdBbGs4RGR1WEJhQ09JTzUiLCJtYWMiOiIxMTI2YmFlZjk5OGUyOTE4YmZjOTQxNTIzZTQwNzlmZjdhNmY4NjViYWYzMjdiODlmZGI2NzY2MDBmZmMxYmIzIiwidGFnIjoiIn0%3D; osool_session=45elmQEyk5dnGrWh4OuNCPeIvLpBPp9yWP9k8LZf</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-205563074\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-497609442 data-indent-pad=\"  \"><span class=sf-dump-note>array:43</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/workspace/projects</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6ImFLL0hEVGdIZlJEVlU3Rm1RSThKMWc9PSIsInZhbHVlIjoiTXk1SUZqeTdvWC84UEhBOEs4d1Nxa3RjSkhlOWdudkt2VDJ2dGtkU1BrS3BUaWRMM3VZRElBV1M5QkYzcEJxQWVCMTM2RVViSFcyU1ZIT1FuRzA1Y1ArVTdOZi80ZlE1bFFrOUllak1kS0JxalIyVGdBbGs4RGR1WEJhQ09JTzUiLCJtYWMiOiIxMTI2YmFlZjk5OGUyOTE4YmZjOTQxNTIzZTQwNzlmZjdhNmY4NjViYWYzMjdiODlmZGI2NzY2MDBmZmMxYmIzIiwidGFnIjoiIn0%3D; osool_session=45elmQEyk5dnGrWh4OuNCPeIvLpBPp9yWP9k8LZf</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51902</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"7 characters\">/logout</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"7 characters\">/logout</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753710331.7376</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753710331</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-497609442\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1077146228 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MoHVz4Tx7nVo7GO6J30zRNXQMrc7mCwjsdZxDGeF</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1077146228\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2040479939 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 13:45:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://osool-b2g.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"346 characters\">XSRF-TOKEN=eyJpdiI6Imo3REFyQ0xWWFhuTU50RDVCa1cyNUE9PSIsInZhbHVlIjoiWlpyb0xYem95SE5HRnJUZnZkZ3pQaTlwb0lXNU9pZkpWMWhpTG8rU3NyQWlMQ3VibzVMSDBkOHFNT0dKbUpKQyIsIm1hYyI6IjRlZGQzZTdjNTJjZWJmNGYyNWE3OWYxZWMzMjlmM2U4MTVlM2Y2NTlkNjQ1Yjc5OTMwYjNkNmYwYzNmYzZjNGUiLCJ0YWciOiIifQ%3D%3D; expires=Mon, 28-Jul-2025 15:45:32 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IlZtcTVpVHJ6Sld3cXJQNGlKS1NOOXc9PSIsInZhbHVlIjoic3BtSlNFT2ZibUk0TGxjQUMvc3daMnlrRjN4OFUySDZCZVhZKzZXOTJ0eEcvWExCWC9yYkFzMmpScVpvS1pZcjVHd3oyR0RBc1F0OUJFVnF0Uk5acDQ5cW9ZbjJpVDBqaVFqWUtydWRVNVAvdkJwVThBUE9iNGt6a25laitTMGMiLCJtYWMiOiIwZjY3NzljYmRmM2YwM2EyNDJhYzBlNzE1OGI2MmRlZWQzMzAwZjUzNmE5MmU1NjM4OTU5NDQ0ZjIxNTAwZjY5IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:45:32 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"318 characters\">XSRF-TOKEN=eyJpdiI6Imo3REFyQ0xWWFhuTU50RDVCa1cyNUE9PSIsInZhbHVlIjoiWlpyb0xYem95SE5HRnJUZnZkZ3pQaTlwb0lXNU9pZkpWMWhpTG8rU3NyQWlMQ3VibzVMSDBkOHFNT0dKbUpKQyIsIm1hYyI6IjRlZGQzZTdjNTJjZWJmNGYyNWE3OWYxZWMzMjlmM2U4MTVlM2Y2NTlkNjQ1Yjc5OTMwYjNkNmYwYzNmYzZjNGUiLCJ0YWciOiIifQ%3D%3D; expires=Mon, 28-Jul-2025 15:45:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IlZtcTVpVHJ6Sld3cXJQNGlKS1NOOXc9PSIsInZhbHVlIjoic3BtSlNFT2ZibUk0TGxjQUMvc3daMnlrRjN4OFUySDZCZVhZKzZXOTJ0eEcvWExCWC9yYkFzMmpScVpvS1pZcjVHd3oyR0RBc1F0OUJFVnF0Uk5acDQ5cW9ZbjJpVDBqaVFqWUtydWRVNVAvdkJwVThBUE9iNGt6a25laitTMGMiLCJtYWMiOiIwZjY3NzljYmRmM2YwM2EyNDJhYzBlNzE1OGI2MmRlZWQzMzAwZjUzNmE5MmU1NjM4OTU5NDQ0ZjIxNTAwZjY5IiwidGFnIjoiIn0%3D; expires=Mon, 28-Jul-2025 15:45:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2040479939\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1118433455 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56eCVpAm7Z7Njid7pxgzHlc33GSeRPOdapi4KDAX</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1118433455\", {\"maxDepth\":0})</script>\n"}}